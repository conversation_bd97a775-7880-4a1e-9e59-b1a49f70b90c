# Gemini CLI 多账户永久配置完整指南

## 📋 目录
- [背景说明](#背景说明)
- [完整配置方案](#完整配置方案)
- [功能说明](#功能说明)
- [使用指南](#使用指南)
- [故障排除](#故障排除)
- [配置文件管理](#配置文件管理)

---

## 背景说明

### 需求背景
Gemini CLI 是 Google 提供的命令行工具，支持两种认证方式：
1. **Google 账号登录**：每天 60 次请求，每分钟 1000 次
2. **API 密钥认证**：每天 100 次请求，每分钟 15 次

### 解决的问题
- **额度限制**：单一认证方式的请求额度有限
- **切换繁琐**：每次切换认证方式需要重新配置环境变量
- **配置丢失**：重启 VS Code 或 Windows 后配置失效
- **多目录使用**：在不同项目目录下无法使用已配置的功能

### 解决方案
通过创建永久的 PowerShell 配置文件，实现：
- ✅ 一次配置，永久使用
- ✅ 快速切换多个认证方式
- ✅ 在任意目录下都可使用
- ✅ 重启系统后自动加载

---

## 完整配置方案

### 步骤 1：创建 PowerShell 配置文件

#### 1.1 检查配置文件路径
```powershell
# 查看 PowerShell 配置文件路径
echo $PROFILE
# 输出示例：C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1
```

#### 1.2 创建配置目录（如果不存在）
```powershell
# 创建配置文件目录
$profileDir = Split-Path $PROFILE -Parent
if (!(Test-Path $profileDir)) { 
    New-Item -ItemType Directory -Path $profileDir -Force 
}
```

#### 1.3 创建配置文件内容

将以下完整代码保存为 PowerShell 配置文件：

```powershell
# ===== 永久 Gemini CLI 配置 =====
# 此文件将在每次启动 PowerShell 时自动加载

# Gemini CLI 配置
$Global:GEMINI_KEY1 = "您的第一个API密钥"
$Global:GEMINI_KEY2 = "您的第二个API密钥"
$Global:GEMINI_PROJECT = "您的Google-Cloud项目ID"

# 切换到 Google 账号模式
function Global:gg {
    $env:GOOGLE_CLOUD_PROJECT = $Global:GEMINI_PROJECT
    $env:GEMINI_API_KEY = $null
    Write-Host "✅ Google 账号模式已激活" -ForegroundColor Green
    Write-Host "📁 项目: $Global:GEMINI_PROJECT" -ForegroundColor Gray
    Write-Host "💡 运行 'gemini' 后选择 'Login with Google'" -ForegroundColor Cyan
}

# 切换到第一个 API 密钥
function Global:gk1 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY1
    Write-Host "✅ API 密钥 1 已激活" -ForegroundColor Green
    Write-Host "🔑 密钥: $($Global:GEMINI_KEY1.Substring(0,15))..." -ForegroundColor Yellow
    Write-Host "💡 运行 'gemini' 后选择 'Use Gemini API Key'" -ForegroundColor Cyan
}

# 切换到第二个 API 密钥
function Global:gk2 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY2
    Write-Host "✅ API 密钥 2 已激活" -ForegroundColor Green
    Write-Host "🔑 密钥: $($Global:GEMINI_KEY2.Substring(0,15))..." -ForegroundColor Yellow
    Write-Host "💡 运行 'gemini' 后选择 'Use Gemini API Key'" -ForegroundColor Cyan
}

# 显示当前状态
function Global:gs {
    Write-Host "=== Gemini 认证状态 ===" -ForegroundColor Magenta
    if ($env:GOOGLE_CLOUD_PROJECT) {
        Write-Host "🔵 当前模式: Google 账号" -ForegroundColor Blue
        Write-Host "📁 项目ID: $env:GOOGLE_CLOUD_PROJECT" -ForegroundColor Gray
        Write-Host "📊 额度: 60次/天, 1000次/分钟" -ForegroundColor Yellow
    } elseif ($env:GEMINI_API_KEY) {
        Write-Host "🔑 当前模式: API 密钥" -ForegroundColor Green
        Write-Host "🔑 密钥: $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Gray
        Write-Host "📊 额度: 100次/天, 15次/分钟" -ForegroundColor Yellow
    } else {
        Write-Host "❌ 未配置认证方式" -ForegroundColor Red
        Write-Host "💡 使用 'gg' 切换到 Google 账号或 'gk1'/'gk2' 切换到 API 密钥" -ForegroundColor Cyan
    }
    Write-Host "========================" -ForegroundColor Magenta
}

# 保存对话上下文
function Global:gem-save {
    param([string]$Message = "保存当前对话上下文")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $contextFile = "GEMINI_CONTEXT.md"
    
    $authMode = if ($env:GOOGLE_CLOUD_PROJECT) { "Google账号" } elseif ($env:GEMINI_API_KEY) { "API密钥" } else { "未配置" }
    
    $content = "`n## 上下文保存 - $timestamp`n**原因**: $Message`n**认证模式**: $authMode`n**当前目录**: $(Get-Location)`n`n---`n"
    
    Add-Content -Path $contextFile -Value $content
    Write-Host "✅ 上下文已保存到 $contextFile" -ForegroundColor Green
}

# 显示 Gemini 帮助
function Global:gem-help {
    Write-Host "`n🎉 Gemini CLI 多账号管理" -ForegroundColor Green
    Write-Host "📋 可用命令：" -ForegroundColor Cyan
    Write-Host "  gs       - 显示当前认证状态" -ForegroundColor Gray
    Write-Host "  gg       - 切换到 Google 账号模式 (60次/天)" -ForegroundColor Gray
    Write-Host "  gk1      - 切换到第一个 API 密钥 (100次/天)" -ForegroundColor Gray
    Write-Host "  gk2      - 切换到第二个 API 密钥 (100次/天)" -ForegroundColor Gray
    Write-Host "  gem-save - 保存当前对话上下文" -ForegroundColor Gray
    Write-Host "  gem-help - 显示此帮助信息" -ForegroundColor Gray
    Write-Host "`n🚀 使用流程：" -ForegroundColor Yellow
    Write-Host "  1. 运行 'gg' 或 'gk1' 选择认证方式" -ForegroundColor Gray
    Write-Host "  2. 运行 'gemini' 启动 CLI" -ForegroundColor Gray
    Write-Host "  3. 根据提示选择对应的认证选项" -ForegroundColor Gray
    Write-Host ""
}

# 快速启动 Gemini 的函数
function Global:gem {
    param([string]$Mode = "google")
    
    switch ($Mode.ToLower()) {
        "google" { gg; gemini }
        "g" { gg; gemini }
        "api1" { gk1; gemini }
        "k1" { gk1; gemini }
        "api2" { gk2; gemini }
        "k2" { gk2; gemini }
        default { 
            Write-Host "用法: gem [google|g|api1|k1|api2|k2]" -ForegroundColor Yellow
            Write-Host "示例:" -ForegroundColor Cyan
            Write-Host "  gem          - 使用 Google 账号启动" -ForegroundColor Gray
            Write-Host "  gem google   - 使用 Google 账号启动" -ForegroundColor Gray
            Write-Host "  gem api1     - 使用第一个 API 密钥启动" -ForegroundColor Gray
            Write-Host "  gem api2     - 使用第二个 API 密钥启动" -ForegroundColor Gray
        }
    }
}

# 初始化：默认设置为 Google 账号模式
gg

# 显示欢迎信息（仅在交互式会话中显示）
if ($Host.UI.RawUI.WindowTitle -notlike "*ISE*" -and [Environment]::UserInteractive) {
    Write-Host "🌟 Gemini CLI 已永久加载！" -ForegroundColor Magenta
    Write-Host "💡 输入 'gem-help' 查看帮助，或直接使用 'gem' 启动" -ForegroundColor Cyan
}

# ===== Gemini CLI 配置结束 =====
```

#### 1.4 保存配置文件
```powershell
# 将配置内容保存到 PowerShell 配置文件
# 方法1：使用记事本编辑
notepad $PROFILE

# 方法2：使用 PowerShell 命令创建
# 将上述代码内容保存到 $PROFILE 路径
```

### 步骤 2：个性化配置

#### 2.1 替换配置信息
在配置文件中替换以下信息：
- `您的第一个API密钥`：替换为实际的 Gemini API 密钥（格式：AIzaSy...）
- `您的第二个API密钥`：替换为另一个 Gemini API 密钥（格式：AIzaSy...）
- `您的Google-Cloud项目ID`：替换为 Google Cloud 项目 ID（格式：project-name-123456）

**示例：**
```powershell
$Global:GEMINI_KEY1 = "AIzaSyDSjvOlRqIIBOIXYX_Zj8jve__JBvIRj4E"
$Global:GEMINI_KEY2 = "AIzaSyBtMgLjj2etiUDEfl5-KDblhDfY0zxSpXU"
$Global:GEMINI_PROJECT = "optimum-shore-439117-v9"
```

#### 2.2 获取 API 密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录 Google 账号
3. 点击 "Create API Key" 创建密钥
4. 复制生成的密钥（格式：AIzaSy...）

#### 2.3 获取 Google Cloud 项目 ID
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 登录 Google 账号
3. 创建新项目或选择现有项目
4. 在项目信息中找到项目 ID
5. 启用 Gemini for Google Cloud API：
   - 进入 "API 和服务" → "库"
   - 搜索 "Gemini" 或 "Cloud AI Companion"
   - 点击启用

### 步骤 3：激活配置
```powershell
# 重新加载配置文件
. $PROFILE

# 或者重启 PowerShell 窗口
```

---

## 功能说明

### 核心命令

| 命令 | 功能 | 说明 |
|------|------|------|
| `gem` | 快速启动（Google 账号） | 最常用，一键启动 |
| `gem api1` | 快速启动（API 密钥 1） | Google 额度用完时使用 |
| `gem api2` | 快速启动（API 密钥 2） | 第一个 API 用完时使用 |
| `gs` | 显示认证状态 | 查看当前使用的认证方式 |
| `gg` | 切换到 Google 账号 | 手动切换认证方式 |
| `gk1` | 切换到 API 密钥 1 | 手动切换认证方式 |
| `gk2` | 切换到 API 密钥 2 | 手动切换认证方式 |
| `gem-save` | 保存对话上下文 | 切换前保存重要信息 |
| `gem-help` | 显示帮助信息 | 查看所有可用命令 |

### 认证方式对比

| 认证方式 | 每日限额 | 每分钟限额 | 优势 | 劣势 |
|----------|----------|------------|------|------|
| Google 账号 | 60 次 | 1000 次 | 高并发，配置简单 | 日限额较低 |
| API 密钥 | 100 次 | 15 次 | 日限额较高，可多账号 | 需要代理，低并发 |

---

## 使用指南

### 一次性设置步骤

#### 第一步：准备工作
```powershell
# 检查 Gemini CLI 是否已安装
gemini --version

# 如未安装，请先安装 Gemini CLI
npm install -g @google/generative-ai-cli
```

#### 第二步：获取认证信息
1. **获取 API 密钥**（建议获取 2 个，来自不同 Google 账号）
2. **创建 Google Cloud 项目**并获取项目 ID
3. **确保网络环境**（API 密钥需要代理，Google 账号不需要）

#### 第三步：创建配置文件
```powershell
# 1. 检查配置文件路径
echo $PROFILE

# 2. 创建配置目录（如果不存在）
$profileDir = Split-Path $PROFILE -Parent
if (!(Test-Path $profileDir)) { New-Item -ItemType Directory -Path $profileDir -Force }

# 3. 编辑配置文件
notepad $PROFILE
```

#### 第四步：配置个人信息
在打开的配置文件中：
1. 复制完整的配置代码
2. 替换 `$Global:GEMINI_KEY1` 为您的第一个 API 密钥
3. 替换 `$Global:GEMINI_KEY2` 为您的第二个 API 密钥
4. 替换 `$Global:GEMINI_PROJECT` 为您的 Google Cloud 项目 ID
5. 保存文件

#### 第五步：测试配置
```powershell
# 重新加载配置
. $PROFILE

# 测试命令
gem-help
gs
gem
```

### 日常使用流程

#### 场景 1：正常使用（推荐）
```powershell
# 直接启动（使用 Google 账号）
gem

# 在 Gemini CLI 中选择 "Login with Google"
# 开始对话...
```

#### 场景 2：Google 账号额度用完
```powershell
# 保存当前上下文
gem-save "Google账号额度已用完，切换到API密钥"

# 退出当前 Gemini 会话（Ctrl+C）

# 切换到 API 密钥模式
gem api1

# 在 Gemini CLI 中选择 "Use Gemini API Key"
# 恢复上下文：请读取 GEMINI_CONTEXT.md 文件
```

#### 场景 3：API 密钥额度用完
```powershell
# 保存上下文并切换到第二个 API 密钥
gem-save "第一个API密钥额度用完"
gem api2
```

#### 场景 4：查看当前状态
```powershell
# 查看当前认证状态
gs

# 输出示例：
# === Gemini 认证状态 ===
# 🔵 当前模式: Google 账号
# 📁 项目ID: your-project-id
# 📊 额度: 60次/天, 1000次/分钟
# ========================
```

### 不同场景使用示例

#### 在 VS Code 中使用
```powershell
# 在 VS Code 集成终端中
PS E:\mycode\project> gem
# 自动加载配置并启动 Gemini
```

#### 在不同目录使用
```powershell
# 在任意目录下
PS C:\Windows\system32> gem api1
PS D:\documents> gs
PS E:\projects\demo> gem-help
```

#### 管理员模式使用
```powershell
# 以管理员身份运行的 PowerShell
PS C:\Windows\system32> gem
# 配置自动加载，正常使用
```

---

## 故障排除

### 常见问题及解决方案

#### 问题 1：命令不识别
```
错误：gs : 无法将"gs"项识别为 cmdlet、函数、脚本文件或可运行程序的名称
```

**解决方案：**
```powershell
# 检查配置文件是否存在
Test-Path $PROFILE

# 手动加载配置
. $PROFILE

# 如果文件不存在，重新创建配置文件
```

#### 问题 2：API 密钥地区限制
```
错误：User location is not supported for the API use
```

**解决方案：**
- 确保使用稳定的 VPN 连接
- 切换到支持的地区（美国、欧洲等）
- 重新创建 API 密钥

#### 问题 3：Google Cloud 项目权限
```
错误：Gemini for Google Cloud API has not been used in project
```

**解决方案：**
```powershell
# 访问 Google Cloud Console
# 启用 Gemini for Google Cloud API
# 确认项目 ID 正确
```

#### 问题 4：配置文件编码问题
```
错误：字符串缺少终止符
```

**解决方案：**
- 使用 UTF-8 编码保存配置文件
- 避免使用特殊字符
- 重新创建配置文件

#### 问题 5：环境变量冲突
```powershell
# 清除可能冲突的环境变量
$env:GOOGLE_CLOUD_PROJECT = $null
$env:GEMINI_API_KEY = $null

# 重新加载配置
. $PROFILE
```

### 调试方法

#### 检查环境变量
```powershell
# 查看当前环境变量
Write-Host "GOOGLE_CLOUD_PROJECT: $env:GOOGLE_CLOUD_PROJECT"
Write-Host "GEMINI_API_KEY: $env:GEMINI_API_KEY"
```

#### 验证配置加载
```powershell
# 检查全局变量
Write-Host "KEY1: $Global:GEMINI_KEY1"
Write-Host "KEY2: $Global:GEMINI_KEY2"
Write-Host "PROJECT: $Global:GEMINI_PROJECT"
```

#### 测试函数可用性
```powershell
# 测试各个函数
Get-Command gg
Get-Command gk1
Get-Command gs
```

---

## 配置文件管理

### 备份配置
```powershell
# 备份当前配置
Copy-Item $PROFILE "$PROFILE.backup.$(Get-Date -Format 'yyyyMMdd')"

# 查看备份文件
Get-ChildItem "$(Split-Path $PROFILE -Parent)\*.backup.*"
```

### 修改配置
```powershell
# 编辑配置文件
notepad $PROFILE

# 或使用 VS Code 编辑
code $PROFILE
```

### 恢复配置
```powershell
# 从备份恢复
Copy-Item "$PROFILE.backup.20241219" $PROFILE -Force

# 重新加载
. $PROFILE
```

### 添加新的 API 密钥
```powershell
# 在配置文件中添加新变量
$Global:GEMINI_KEY3 = "新的API密钥"

# 添加新的切换函数
function Global:gk3 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY3
    Write-Host "✅ API 密钥 3 已激活" -ForegroundColor Green
}
```

### 删除配置
```powershell
# 删除配置文件
Remove-Item $PROFILE

# 或重命名禁用
Rename-Item $PROFILE "$PROFILE.disabled"
```

### 配置文件位置
- **Windows 10/11**: `C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1`
- **PowerShell Core**: `C:\Users\<USER>\Documents\PowerShell\Microsoft.PowerShell_profile.ps1`

---

## 总结

通过本指南，您可以实现：

✅ **一次配置，永久使用** - 配置文件自动加载  
✅ **多账户无缝切换** - 支持 Google 账号 + 多个 API 密钥  
✅ **全局可用** - 在任意目录和 PowerShell 窗口中使用  
✅ **智能管理** - 自动状态显示和上下文保存  
✅ **故障恢复** - 完整的故障排除和配置管理方案  

现在您可以高效地使用 Gemini CLI，不再受到单一账户额度限制的困扰！

---

*最后更新：2024年12月19日*
