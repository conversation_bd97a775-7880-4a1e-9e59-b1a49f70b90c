# ===== 永久 Gemini CLI 配置 =====
# 此文件将在每次启动 PowerShell 时自动加载

# Gemini CLI 配置
$Global:GEMINI_KEY1 = "AIzaSyDSjvOlRqIIBOIXYX_Zj8jve__JBvIRj4E"
$Global:GEMINI_KEY2 = "AIzaSyBtMgLjj2etiUDEfl5-KDblhDfY0zxSpXU"
$Global:GEMINI_PROJECT = "optimum-shore-439117-v9"

# 切换到 Google 账号模式
function Global:gg {
    $env:GOOGLE_CLOUD_PROJECT = $Global:GEMINI_PROJECT
    $env:GEMINI_API_KEY = $null
    Write-Host "✅ Google 账号模式已激活" -ForegroundColor Green
    Write-Host "📁 项目: $Global:GEMINI_PROJECT" -ForegroundColor Gray
    Write-Host "💡 运行 'gemini' 后选择 'Login with Google'" -ForegroundColor Cyan
}

# 切换到第一个 API 密钥
function Global:gk1 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY1
    Write-Host "✅ API 密钥 1 已激活" -ForegroundColor Green
    Write-Host "🔑 密钥: $($Global:GEMINI_KEY1.Substring(0,15))..." -ForegroundColor Yellow
    Write-Host "💡 运行 'gemini' 后选择 'Use Gemini API Key'" -ForegroundColor Cyan
}

# 切换到第二个 API 密钥
function Global:gk2 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY2
    Write-Host "✅ API 密钥 2 已激活" -ForegroundColor Green
    Write-Host "🔑 密钥: $($Global:GEMINI_KEY2.Substring(0,15))..." -ForegroundColor Yellow
    Write-Host "💡 运行 'gemini' 后选择 'Use Gemini API Key'" -ForegroundColor Cyan
}

# 显示当前状态
function Global:gs {
    Write-Host "=== Gemini 认证状态 ===" -ForegroundColor Magenta
    if ($env:GOOGLE_CLOUD_PROJECT) {
        Write-Host "🔵 当前模式: Google 账号" -ForegroundColor Blue
        Write-Host "📁 项目ID: $env:GOOGLE_CLOUD_PROJECT" -ForegroundColor Gray
        Write-Host "📊 额度: 60次/天, 1000次/分钟" -ForegroundColor Yellow
    } elseif ($env:GEMINI_API_KEY) {
        Write-Host "🔑 当前模式: API 密钥" -ForegroundColor Green
        Write-Host "🔑 密钥: $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Gray
        Write-Host "📊 额度: 100次/天, 15次/分钟" -ForegroundColor Yellow
    } else {
        Write-Host "❌ 未配置认证方式" -ForegroundColor Red
        Write-Host "💡 使用 'gg' 切换到 Google 账号或 'gk1'/'gk2' 切换到 API 密钥" -ForegroundColor Cyan
    }
    Write-Host "========================" -ForegroundColor Magenta
}

# 保存对话上下文
function Global:gem-save {
    param([string]$Message = "保存当前对话上下文")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $contextFile = "GEMINI_CONTEXT.md"
    
    $authMode = if ($env:GOOGLE_CLOUD_PROJECT) { "Google账号" } elseif ($env:GEMINI_API_KEY) { "API密钥" } else { "未配置" }
    
    $content = "`n## 上下文保存 - $timestamp`n**原因**: $Message`n**认证模式**: $authMode`n**当前目录**: $(Get-Location)`n`n---`n"
    
    Add-Content -Path $contextFile -Value $content
    Write-Host "✅ 上下文已保存到 $contextFile" -ForegroundColor Green
}

# 显示 Gemini 帮助
function Global:gem-help {
    Write-Host "`n🎉 Gemini CLI 多账号管理" -ForegroundColor Green
    Write-Host "📋 可用命令：" -ForegroundColor Cyan
    Write-Host "  gs       - 显示当前认证状态" -ForegroundColor Gray
    Write-Host "  gg       - 切换到 Google 账号模式 (60次/天)" -ForegroundColor Gray
    Write-Host "  gk1      - 切换到第一个 API 密钥 (100次/天)" -ForegroundColor Gray
    Write-Host "  gk2      - 切换到第二个 API 密钥 (100次/天)" -ForegroundColor Gray
    Write-Host "  gem-save - 保存当前对话上下文" -ForegroundColor Gray
    Write-Host "  gem-help - 显示此帮助信息" -ForegroundColor Gray
    Write-Host "`n🚀 使用流程：" -ForegroundColor Yellow
    Write-Host "  1. 运行 'gg' 或 'gk1' 选择认证方式" -ForegroundColor Gray
    Write-Host "  2. 运行 'gemini' 启动 CLI" -ForegroundColor Gray
    Write-Host "  3. 根据提示选择对应的认证选项" -ForegroundColor Gray
    Write-Host ""
}

# 快速启动 Gemini 的函数
function Global:gem {
    param([string]$Mode = "google")
    
    switch ($Mode.ToLower()) {
        "google" { gg; gemini }
        "g" { gg; gemini }
        "api1" { gk1; gemini }
        "k1" { gk1; gemini }
        "api2" { gk2; gemini }
        "k2" { gk2; gemini }
        default { 
            Write-Host "用法: gem [google|g|api1|k1|api2|k2]" -ForegroundColor Yellow
            Write-Host "示例:" -ForegroundColor Cyan
            Write-Host "  gem          - 使用 Google 账号启动" -ForegroundColor Gray
            Write-Host "  gem google   - 使用 Google 账号启动" -ForegroundColor Gray
            Write-Host "  gem api1     - 使用第一个 API 密钥启动" -ForegroundColor Gray
            Write-Host "  gem api2     - 使用第二个 API 密钥启动" -ForegroundColor Gray
        }
    }
}

# 初始化：默认设置为 Google 账号模式
gg

# 显示欢迎信息（仅在交互式会话中显示）
if ($Host.UI.RawUI.WindowTitle -notlike "*ISE*" -and [Environment]::UserInteractive) {
    Write-Host "🌟 Gemini CLI 已永久加载！" -ForegroundColor Magenta
    Write-Host "💡 输入 'gem-help' 查看帮助，或直接使用 'gem' 启动" -ForegroundColor Cyan
}

# ===== Gemini CLI 配置结束 =====
