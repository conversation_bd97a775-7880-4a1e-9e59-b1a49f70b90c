# A股趋势股分析工具 - 近3个月表现最佳股票分析
# 创建日期：2025-07-26

import json
import os
from datetime import datetime, timedelta
from collections import defaultdict

def parse_value(s):
    """将'亿', '万', '%'等单位的字符串转换为数值"""
    s = str(s).strip()
    if not s or s == 'N/A':
        return 0
    if '%' in s:
        return float(s.replace('%', ''))
    s_lower = s.lower()
    if '亿' in s_lower:
        return float(s_lower.replace('亿', '')) * 100000000
    if '万' in s_lower:
        return float(s_lower.replace('万', '')) * 10000
    try:
        return float(s)
    except (ValueError, TypeError):
        return 0

def load_stock_data(file_path):
    """加载股票数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"无法读取或解析文件: {e}")
        return None

def analyze_trend_stocks(data, days_back=90):
    """分析近期表现最好的趋势股"""
    if not data or 'dates' not in data:
        print("数据格式错误")
        return []
    
    # 获取最近的日期范围
    all_dates = sorted(data['dates'].keys())
    if not all_dates:
        print("没有找到日期数据")
        return []
    
    # 计算目标日期范围（近3个月）
    latest_date = datetime.strptime(all_dates[-1], '%Y-%m-%d')
    start_date = latest_date - timedelta(days=days_back)
    
    target_dates = [date for date in all_dates 
                   if datetime.strptime(date, '%Y-%m-%d') >= start_date]
    
    print(f"分析时间范围: {target_dates[0]} 到 {target_dates[-1]} (共{len(target_dates)}个交易日)")
    
    # 收集所有股票的表现数据
    stock_performance = defaultdict(list)
    
    for date_str in target_dates:
        daily_data = data['dates'].get(date_str, {})
        stock_list_data = daily_data.get('data', {}).get('data', {}).get('stock_list', {})
        head_info = stock_list_data.get('head_info', [])
        stock_list = stock_list_data.get('list', [])
        
        if not head_info or not stock_list:
            continue
            
        # 创建表头映射
        header_map = {item['text']: i for i, item in enumerate(head_info)}
        
        # 检查必要字段
        required_fields = ['股票', '涨幅', '竞价成交', '最终额', '流通值']
        if not all(field in header_map for field in required_fields):
            continue
            
        for stock_data in stock_list:
            try:
                stock_name = stock_data[header_map['股票']].get('text', 'N/A')
                if stock_name == 'N/A':
                    continue
                    
                # 提取关键数据
                daily_return = parse_value(stock_data[header_map['涨幅']].get('rank', 0))
                turnover = parse_value(stock_data[header_map['竞价成交']].get('rank', 0))
                final_amount = parse_value(stock_data[header_map['最终额']].get('rank', 0))
                market_cap = parse_value(stock_data[header_map['流通值']].get('rank', 0))
                
                stock_performance[stock_name].append({
                    'date': date_str,
                    'daily_return': daily_return,
                    'turnover': turnover,
                    'final_amount': final_amount,
                    'market_cap': market_cap,
                    'raw_data': stock_data
                })
                
            except (IndexError, KeyError, TypeError):
                continue
    
    return stock_performance, target_dates

def calculate_trend_metrics(stock_performance, min_days=5):
    """计算趋势指标"""
    trend_analysis = []
    
    for stock_name, daily_data in stock_performance.items():
        if len(daily_data) < min_days:  # 至少要有10个交易日的数据
            continue
            
        # 按日期排序
        daily_data.sort(key=lambda x: x['date'])
        
        # 计算累计收益率
        total_return = sum(d['daily_return'] for d in daily_data)
        
        # 计算连阳天数
        consecutive_up_days = 0
        max_consecutive_up = 0
        current_consecutive = 0
        
        for d in daily_data:
            if d['daily_return'] > 0:
                current_consecutive += 1
                max_consecutive_up = max(max_consecutive_up, current_consecutive)
            else:
                current_consecutive = 0
        
        # 计算平均成交额
        avg_turnover = sum(d['turnover'] for d in daily_data) / len(daily_data)
        
        # 计算涨停次数
        limit_up_days = sum(1 for d in daily_data if d['daily_return'] >= 9.8)
        
        # 计算最大回撤
        cumulative_returns = []
        cumulative = 0
        for d in daily_data:
            cumulative += d['daily_return']
            cumulative_returns.append(cumulative)
        
        max_drawdown = 0
        peak = cumulative_returns[0]
        for ret in cumulative_returns:
            if ret > peak:
                peak = ret
            drawdown = peak - ret
            max_drawdown = max(max_drawdown, drawdown)
        
        # 计算胜率
        win_rate = sum(1 for d in daily_data if d['daily_return'] > 0) / len(daily_data) * 100
        
        # 获取最新市值
        latest_market_cap = daily_data[-1]['market_cap'] if daily_data else 0
        
        trend_analysis.append({
            'stock_name': stock_name,
            'total_return': total_return,
            'trading_days': len(daily_data),
            'avg_daily_return': total_return / len(daily_data),
            'max_consecutive_up': max_consecutive_up,
            'limit_up_days': limit_up_days,
            'avg_turnover': avg_turnover,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'market_cap': latest_market_cap,
            'daily_data': daily_data
        })
    
    return trend_analysis

def print_top_performers(trend_analysis, top_n=20):
    """打印表现最好的股票"""
    # 按总收益率排序
    sorted_stocks = sorted(trend_analysis, key=lambda x: x['total_return'], reverse=True)
    
    print(f"\n{'='*120}")
    print(f"A股近3个月表现最佳趋势股 TOP {top_n}")
    print(f"{'='*120}")
    
    print(f"{'排名':<4}{'股票名称':<12}{'累计涨幅':<10}{'交易天数':<8}{'日均涨幅':<10}{'最大连阳':<8}{'涨停次数':<8}{'胜率':<8}{'最大回撤':<10}{'平均成交额':<12}")
    print("-" * 120)
    
    for i, stock in enumerate(sorted_stocks[:top_n], 1):
        print(f"{i:<4}{stock['stock_name']:<12}{stock['total_return']:.2f}%{stock['trading_days']:<8}"
              f"{stock['avg_daily_return']:.2f}%{stock['max_consecutive_up']:<8}{stock['limit_up_days']:<8}"
              f"{stock['win_rate']:.1f}%{stock['max_drawdown']:.2f}%{stock['avg_turnover']/100000000:.2f}亿")
    
    return sorted_stocks[:top_n]

def analyze_volume_characteristics(stock_data):
    """分析成交量特征"""
    turnovers = [d['turnover'] for d in stock_data]
    if not turnovers:
        return {}

    avg_turnover = sum(turnovers) / len(turnovers)
    max_turnover = max(turnovers)

    # 计算倍量天数（成交量超过平均值2倍）
    volume_spike_days = sum(1 for t in turnovers if t > avg_turnover * 2)

    # 计算放量上涨天数
    volume_up_days = 0
    for i, d in enumerate(stock_data):
        if d['daily_return'] > 0 and d['turnover'] > avg_turnover:
            volume_up_days += 1

    return {
        'avg_turnover': avg_turnover,
        'max_turnover': max_turnover,
        'volume_spike_days': volume_spike_days,
        'volume_up_days': volume_up_days,
        'volume_consistency': volume_up_days / len([d for d in stock_data if d['daily_return'] > 0]) if any(d['daily_return'] > 0 for d in stock_data) else 0
    }

def calculate_moving_averages(stock_data, periods=[5, 10, 20]):
    """计算移动平均线"""
    if len(stock_data) < max(periods):
        return {}

    # 计算累计收益率序列
    cumulative_returns = []
    cumulative = 0
    for d in stock_data:
        cumulative += d['daily_return']
        cumulative_returns.append(cumulative)

    ma_data = {}
    for period in periods:
        if len(cumulative_returns) >= period:
            ma_values = []
            for i in range(period-1, len(cumulative_returns)):
                ma_value = sum(cumulative_returns[i-period+1:i+1]) / period
                ma_values.append(ma_value)
            ma_data[f'MA{period}'] = ma_values

    return ma_data

def analyze_trend_strength(stock_data):
    """分析趋势强度"""
    if len(stock_data) < 10:
        return {}

    # 计算新高次数
    cumulative_returns = []
    cumulative = 0
    for d in stock_data:
        cumulative += d['daily_return']
        cumulative_returns.append(cumulative)

    new_high_count = 0
    for i in range(1, len(cumulative_returns)):
        if cumulative_returns[i] > max(cumulative_returns[:i]):
            new_high_count += 1

    # 计算回调表现（从最高点回调的最大幅度）
    max_return = max(cumulative_returns)
    max_index = cumulative_returns.index(max_return)

    max_pullback = 0
    if max_index < len(cumulative_returns) - 1:
        for i in range(max_index + 1, len(cumulative_returns)):
            pullback = max_return - cumulative_returns[i]
            max_pullback = max(max_pullback, pullback)

    return {
        'new_high_count': new_high_count,
        'new_high_ratio': new_high_count / len(stock_data),
        'max_pullback': max_pullback,
        'trend_consistency': new_high_count / (len(stock_data) * 0.3)  # 趋势一致性指标
    }

def analyze_top_stock_characteristics(top_stocks):
    """分析顶级股票的特征"""
    print(f"\n{'='*80}")
    print("顶级趋势股特征分析")
    print(f"{'='*80}")

    # 统计分析
    total_returns = [s['total_return'] for s in top_stocks]
    win_rates = [s['win_rate'] for s in top_stocks]
    max_consecutive_ups = [s['max_consecutive_up'] for s in top_stocks]
    limit_up_counts = [s['limit_up_days'] for s in top_stocks]

    print(f"📈 累计涨幅分析:")
    print(f"  平均累计涨幅: {sum(total_returns)/len(total_returns):.2f}%")
    print(f"  最高累计涨幅: {max(total_returns):.2f}%")
    print(f"  最低累计涨幅: {min(total_returns):.2f}%")

    print(f"\n🎯 胜率分析:")
    print(f"  平均胜率: {sum(win_rates)/len(win_rates):.1f}%")
    print(f"  最高胜率: {max(win_rates):.1f}%")
    print(f"  胜率>70%的股票数量: {sum(1 for wr in win_rates if wr > 70)}")

    print(f"\n🔥 连阳特征:")
    print(f"  平均最大连阳天数: {sum(max_consecutive_ups)/len(max_consecutive_ups):.1f}天")
    print(f"  最大连阳天数: {max(max_consecutive_ups)}天")
    print(f"  连阳>5天的股票数量: {sum(1 for cu in max_consecutive_ups if cu > 5)}")

    print(f"\n📊 涨停特征:")
    print(f"  平均涨停次数: {sum(limit_up_counts)/len(limit_up_counts):.1f}次")
    print(f"  最多涨停次数: {max(limit_up_counts)}次")
    print(f"  有涨停的股票数量: {sum(1 for luc in limit_up_counts if luc > 0)}")

    # 成交量分析
    print(f"\n💰 成交量特征分析:")
    volume_data = []
    for stock in top_stocks:
        vol_analysis = analyze_volume_characteristics(stock['daily_data'])
        if vol_analysis:
            volume_data.append(vol_analysis)

    if volume_data:
        avg_volume_spikes = sum(v['volume_spike_days'] for v in volume_data) / len(volume_data)
        avg_volume_consistency = sum(v['volume_consistency'] for v in volume_data) / len(volume_data)
        print(f"  平均倍量天数: {avg_volume_spikes:.1f}天")
        print(f"  平均量价配合度: {avg_volume_consistency:.2f}")

    # 趋势强度分析
    print(f"\n🚀 趋势强度分析:")
    trend_data = []
    for stock in top_stocks:
        trend_analysis = analyze_trend_strength(stock['daily_data'])
        if trend_analysis:
            trend_data.append(trend_analysis)

    if trend_data:
        avg_new_high_ratio = sum(t['new_high_ratio'] for t in trend_data) / len(trend_data)
        avg_pullback = sum(t['max_pullback'] for t in trend_data) / len(trend_data)
        print(f"  平均创新高比例: {avg_new_high_ratio:.2f}")
        print(f"  平均最大回调: {avg_pullback:.2f}%")

def detailed_stock_analysis(stock_data, stock_name):
    """详细分析单只股票"""
    print(f"\n{'='*60}")
    print(f"📊 {stock_name} 详细技术分析")
    print(f"{'='*60}")

    # 基础数据
    daily_data = stock_data['daily_data']
    print(f"分析周期: {daily_data[0]['date']} 至 {daily_data[-1]['date']}")
    print(f"交易天数: {len(daily_data)}天")
    print(f"累计涨幅: {stock_data['total_return']:.2f}%")
    print(f"日均涨幅: {stock_data['avg_daily_return']:.2f}%")

    # 成交量分析
    volume_analysis = analyze_volume_characteristics(daily_data)
    if volume_analysis:
        print(f"\n💰 成交量分析:")
        print(f"  平均成交额: {volume_analysis['avg_turnover']/100000000:.2f}亿")
        print(f"  最大成交额: {volume_analysis['max_turnover']/100000000:.2f}亿")
        print(f"  倍量天数: {volume_analysis['volume_spike_days']}天")
        print(f"  放量上涨天数: {volume_analysis['volume_up_days']}天")
        print(f"  量价配合度: {volume_analysis['volume_consistency']:.2f}")

    # 趋势分析
    trend_analysis = analyze_trend_strength(daily_data)
    if trend_analysis:
        print(f"\n🚀 趋势强度分析:")
        print(f"  创新高次数: {trend_analysis['new_high_count']}次")
        print(f"  创新高比例: {trend_analysis['new_high_ratio']:.2f}")
        print(f"  最大回调: {trend_analysis['max_pullback']:.2f}%")
        print(f"  趋势一致性: {trend_analysis['trend_consistency']:.2f}")

    # 连续表现分析
    print(f"\n📈 连续表现:")
    print(f"  最大连阳: {stock_data['max_consecutive_up']}天")
    print(f"  涨停次数: {stock_data['limit_up_days']}次")
    print(f"  胜率: {stock_data['win_rate']:.1f}%")
    print(f"  最大回撤: {stock_data['max_drawdown']:.2f}%")

    # 近期表现（最后5个交易日）
    recent_data = daily_data[-5:]
    recent_return = sum(d['daily_return'] for d in recent_data)
    print(f"\n🔥 近5日表现:")
    print(f"  近5日累计涨幅: {recent_return:.2f}%")
    print(f"  近5日平均成交额: {sum(d['turnover'] for d in recent_data)/len(recent_data)/100000000:.2f}亿")

    # 日线明细（显示最后10个交易日）
    print(f"\n📅 最近10个交易日明细:")
    print(f"{'日期':<12}{'涨幅':<8}{'成交额':<10}{'封单额':<10}{'市值':<10}")
    print("-" * 60)
    for d in daily_data[-10:]:
        print(f"{d['date']:<12}{d['daily_return']:.2f}%{d['turnover']/100000000:.2f}亿{d['final_amount']/100000000:.2f}亿{d['market_cap']/100000000:.0f}亿")

def generate_investment_suggestions(top_stocks):
    """生成投资建议"""
    print(f"\n{'='*80}")
    print("🎯 投资策略建议")
    print(f"{'='*80}")

    # 分类股票
    high_momentum = [s for s in top_stocks if s['total_return'] > 50 and s['max_consecutive_up'] >= 5]
    stable_growth = [s for s in top_stocks if s['win_rate'] > 70 and s['max_drawdown'] < 15]
    volume_leaders = [s for s in top_stocks if s['avg_turnover'] > 500000000]  # 5亿以上

    print(f"📊 股票分类:")
    print(f"  高动量股票 (涨幅>50%且连阳≥5天): {len(high_momentum)}只")
    if high_momentum:
        print(f"    代表股票: {', '.join([s['stock_name'] for s in high_momentum[:3]])}")

    print(f"  稳健成长股票 (胜率>70%且回撤<15%): {len(stable_growth)}只")
    if stable_growth:
        print(f"    代表股票: {', '.join([s['stock_name'] for s in stable_growth[:3]])}")

    print(f"  成交活跃股票 (日均成交>5亿): {len(volume_leaders)}只")
    if volume_leaders:
        print(f"    代表股票: {', '.join([s['stock_name'] for s in volume_leaders[:3]])}")

    print(f"\n💡 投资策略建议:")
    print(f"1. 趋势跟踪策略: 关注高动量股票，在回调时分批建仓")
    print(f"2. 稳健投资策略: 选择胜率高、回撤小的股票长期持有")
    print(f"3. 短线交易策略: 利用成交活跃股票的波动进行短线操作")
    print(f"4. 风险控制: 单只股票仓位不超过总资金的10%")
    print(f"5. 止损策略: 设置15%的止损线，严格执行")

def main():
    """主函数"""
    print("🚀 A股趋势股分析工具启动...")
    print("分析目标：近3个月表现最好的趋势股及其特征")

    # 数据文件路径
    file_path = os.path.join('数据分析测试', '涨停抢筹_所有历史数据.json')

    # 加载数据
    data = load_stock_data(file_path)
    if not data:
        return

    # 分析趋势股
    stock_performance, target_dates = analyze_trend_stocks(data, days_back=90)

    if not stock_performance:
        print("没有找到符合条件的股票数据")
        return

    # 计算趋势指标
    trend_analysis = calculate_trend_metrics(stock_performance, min_days=5)

    if not trend_analysis:
        print("没有找到符合最小交易天数要求的股票")
        return

    # 打印结果
    top_performers = print_top_performers(trend_analysis, top_n=20)

    # 特征分析
    analyze_top_stock_characteristics(top_performers[:10])

    # 详细分析前3名股票
    print(f"\n{'='*80}")
    print("🔍 TOP 3 股票详细分析")
    print(f"{'='*80}")

    for i, stock in enumerate(top_performers[:3], 1):
        detailed_stock_analysis(stock, f"第{i}名: {stock['stock_name']}")

    # 投资建议
    generate_investment_suggestions(top_performers[:10])

    print(f"\n✅ 分析完成！共分析了 {len(trend_analysis)} 只股票")
    print(f"数据来源: {file_path}")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
