# Global Gemini CLI Loader - Can be called from anywhere

param(
    [switch]$Help,
    [switch]$Status
)

# Configuration path
$ConfigPath = "E:\mycode\wuzhuo\clean-gemini-config.ps1"

if ($Help) {
    Write-Host "=== Gemini CLI Global Loader ===" -ForegroundColor Cyan
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  E:\mycode\wuzhuo\global-gemini-loader.ps1" -ForegroundColor Gray
    Write-Host "  Or create alias: Set-Alias gem-load E:\mycode\wuzhuo\global-gemini-loader.ps1" -ForegroundColor Gray
    Write-Host ""
    Write-Host "After loading, available commands:" -ForegroundColor Yellow
    Write-Host "  gs  - Show status" -ForegroundColor Gray
    Write-Host "  gg  - Google account mode" -ForegroundColor Gray
    Write-Host "  gk1 - API key 1" -ForegroundColor Gray
    Write-Host "  gk2 - API key 2" -ForegroundColor Gray
    return
}

if ($Status) {
    if ($env:GOOGLE_CLOUD_PROJECT) {
        Write-Host "Current: Google Account Mode" -ForegroundColor Blue
        Write-Host "Project: $env:GOOGLE_CLOUD_PROJECT" -ForegroundColor Gray
    } elseif ($env:GEMINI_API_KEY) {
        Write-Host "Current: API Key Mode" -ForegroundColor Green
        Write-Host "Key: $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Gray
    } else {
        Write-Host "Status: Not configured" -ForegroundColor Red
        Write-Host "Run without -Status to load configuration" -ForegroundColor Yellow
    }
    return
}

Write-Host "Loading Gemini CLI from global location..." -ForegroundColor Cyan
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Gray

if (Test-Path $ConfigPath) {
    try {
        . $ConfigPath
        Write-Host "Successfully loaded Gemini CLI configuration!" -ForegroundColor Green
        Write-Host "You can now use: gs, gg, gk1, gk2 commands" -ForegroundColor Yellow
    } catch {
        Write-Host "Error loading configuration: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Configuration file not found at: $ConfigPath" -ForegroundColor Red
    Write-Host "Please ensure the file exists" -ForegroundColor Yellow
}
