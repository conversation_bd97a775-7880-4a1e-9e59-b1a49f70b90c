# Augment Code 全局规则配置

## 基础交流规则
- 请始终保持中文交流
- 所有回复、解释、注释都使用中文
- 技术术语可以保留英文，但需要提供中文解释

## 对话记录管理
- 在重要的技术讨论后，主动询问是否需要保存对话记录
- 建议将重要的技术决策和解决方案记录到项目文档中
- 对于复杂的问题解决过程，建议创建技术文档备份

## Git 提交信息生成规则

### 核心原则
Git提交信息生成的**首要目的**是作为开发者继续工作的上下文，其次才是作为产品面向用户功能升级的说明。

### 必须包含的内容

#### 1. 提交标题
- 遵循 Conventional Commits 格式
- 使用标准前缀：
  - `feat:` - 新功能
  - `fix:` - Bug修复
  - `perf:` - 性能优化
  - `refactor:` - 代码重构
  - `docs:` - 文档更新
  - `style:` - 代码格式调整
  - `test:` - 测试相关
  - `chore:` - 构建过程或辅助工具的变动
- 标题简洁明了，不超过50个字符

#### 2. 详细功能变更说明
- 每个修改点都要有具体描述
- 真实记录修改的关键信息
- 包括修改前后的对比
- 说明修改的原因和影响范围

#### 3. 性能优化数据（可选）
- **重要**：必须有对应的测试结果为依据
- 如果没有相应的测试，就不写此项
- 示例格式：
  - "查询速度提升80%（从500ms降至100ms）"
  - "缓存命中率提升至95%"
  - "内存使用量减少30%"

#### 4. Bug修复记录
- 具体修复了什么问题
- 问题的根本原因
- 修复方法和步骤
- 影响范围和测试验证

#### 5. 版本号建议
- 基于修改规模建议版本号
- 遵循语义化版本控制：
  - 主版本号：不兼容的API修改
  - 次版本号：向下兼容的功能性新增
  - 修订号：向下兼容的问题修正

### 重大更新处理规则

当满足以下条件之一时，需要创建详细的更新文档：
- 更新内容超过10个修改点
- 修复了关键的安全问题或核心功能问题
- 有重大技术突破或架构调整
- 新增重要功能模块

#### 更新文档要求
- 文档命名格式：`CHANGELOG_v{版本号}.md`
- 文档内容必须包括：
  - 更新概述
  - 详细的修改列表
  - 开发过程中的技术决策
  - 测试结果和验证数据
  - 已知问题和后续计划
- 在Git提交信息中注明"详见CHANGELOG_v{版本号}.md"

### 提交信息模板

```
{type}: {简洁的标题}

## 功能变更
- [修改点1] 具体描述修改内容和原因
- [修改点2] 修改前后的对比说明
- [修改点3] 影响范围和注意事项

## 性能优化（如有测试数据）
- 优化项目：具体数据对比

## Bug修复
- 问题描述：具体问题现象
- 根本原因：问题分析结果
- 修复方法：具体解决步骤
- 验证结果：测试确认情况

## 版本建议
建议版本号：v{x.y.z}

## 其他说明
- 兼容性影响
- 部署注意事项
- 后续工作计划
```

## 代码开发规则
- 优先考虑中国网络环境的兼容性
- 在Windows环境下开发时注意路径分隔符
- 代码注释使用中文
- 变量和函数命名可以使用英文，但要有中文注释说明
- 考虑代理/VPN环境下的网络配置

## 文档管理规则
- 重要的配置和解决方案要形成文档
- 技术决策要有记录和说明
- 定期整理和更新技术文档
- 保持文档的可搜索性和可维护性
