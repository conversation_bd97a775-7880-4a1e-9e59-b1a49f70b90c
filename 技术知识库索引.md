# 技术知识库索引

## 📋 文档分类

### Gemini CLI 相关
- **主文档**: `Gemini-CLI-多账户配置指南.md` - 完整配置指南
- **对话记录**: `Gemini-CLI配置对话记录_完整版.md` - 详细对话过程
- **配置文件**: `Microsoft.PowerShell_profile.ps1` - 永久配置
- **备用配置**: `clean-gemini-config.ps1` - 简化版配置

### PowerShell 脚本
- **全局配置**: `global-gemini-config-fixed.ps1`
- **启动脚本**: `start-gemini.ps1`
- **加载器**: `global-gemini-loader.ps1`

---

## 🔍 快速查找指南

### 按问题类型查找

#### 配置问题
- **初次配置**: 查看 `Gemini-CLI-多账户配置指南.md` 第2章
- **配置失效**: 查看故障排除部分
- **跨目录使用**: 查看永久配置方案

#### 认证问题
- **API密钥错误**: 检查地区限制和代理设置
- **Google账号问题**: 检查项目ID和API启用状态
- **环境变量冲突**: 使用 `gs` 命令检查状态

#### 使用问题
- **命令不识别**: 重新加载配置文件 `. $PROFILE`
- **额度限制**: 使用多账户切换方案
- **上下文丢失**: 使用 `gem-save` 保存重要信息

### 按使用场景查找

#### 日常使用
- **快速启动**: `gem`
- **状态检查**: `gs`
- **帮助信息**: `gem-help`

#### 额度管理
- **Google账号**: `gem` 或 `gg`
- **API密钥1**: `gem api1` 或 `gk1`
- **API密钥2**: `gem api2` 或 `gk2`

#### 上下文管理
- **保存上下文**: `gem-save "原因"`
- **恢复上下文**: 在新会话中读取 `GEMINI_CONTEXT.md`

---

## 📊 技术要点速查

### 核心命令
```powershell
gem          # 快速启动（Google账号）
gem api1     # 快速启动（API密钥1）
gem api2     # 快速启动（API密钥2）
gs           # 显示状态
gg           # 切换Google账号
gk1/gk2      # 切换API密钥
gem-save     # 保存上下文
gem-help     # 显示帮助
```

### 配置文件位置
```
Windows: C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1
```

### 环境变量
```powershell
$env:GOOGLE_CLOUD_PROJECT  # Google Cloud 项目ID
$env:GEMINI_API_KEY        # Gemini API 密钥
```

### 认证方式对比
| 方式 | 日限额 | 分钟限额 | 优势 | 劣势 |
|------|--------|----------|------|------|
| Google账号 | 60次 | 1000次 | 高并发 | 日限额低 |
| API密钥 | 100次 | 15次 | 日限额高 | 需代理 |

---

## 🔄 版本历史

### v1.0 (2024-12-19)
- 初始配置方案
- 基本多账户切换功能

### v2.0 (2024-12-19)
- 永久配置文件方案
- 全局可用功能
- 完整故障排除

### v3.0 (2024-12-19)
- 技术文档完善
- 知识库索引创建
- 最佳实践总结

---

## 📝 使用记录模板

### 新问题记录
```markdown
## 问题描述
[详细描述遇到的问题]

## 解决方案
[记录解决步骤]

## 相关文件
[列出相关的配置文件或文档]

## 经验教训
[总结经验和注意事项]
```

### 配置变更记录
```markdown
## 变更日期
[YYYY-MM-DD]

## 变更内容
[描述具体变更]

## 变更原因
[说明变更原因]

## 影响范围
[说明影响的功能或使用场景]
```

---

*索引创建时间: 2024年12月19日*
