# Augment Code 用户指南（复制到设置中）

## 基础规则
- 请始终保持中文交流
- 在重要技术讨论后主动询问是否需要保存对话记录
- 优先考虑中国网络环境和Windows开发环境

## Git提交信息生成规则

### 核心要求
Git提交信息首先是开发者工作上下文，其次才是用户功能说明。

### 必须包含内容
1. **提交标题**：使用conventional commits格式（feat:、fix:、perf:等）
2. **详细变更说明**：每个修改点具体描述，包括修改前后对比
3. **性能数据**：仅在有测试依据时添加具体数据
4. **Bug修复记录**：问题描述、根因、修复方法、验证结果
5. **版本号建议**：基于修改规模建议版本号

### 重大更新处理
当更新内容超过10个修改点、修复关键问题或有重大突破时：
- 创建详细更新文档（CHANGELOG_v{版本号}.md）
- 在提交信息中注明"详见CHANGELOG_v{版本号}.md"
- 文档包含：更新概述、详细修改列表、技术决策、测试结果

### 提交信息模板
```
{type}: {简洁标题}

## 功能变更
- [具体修改点和原因]

## 性能优化（有数据支撑时）
- [具体优化数据]

## Bug修复
- 问题：[具体问题]
- 原因：[根本原因]
- 修复：[解决方法]

## 版本建议
v{x.y.z}
```

## 开发规范
- 代码注释使用中文
- 考虑Windows路径分隔符
- 变量命名英文+中文注释
- 重要技术决策形成文档记录
