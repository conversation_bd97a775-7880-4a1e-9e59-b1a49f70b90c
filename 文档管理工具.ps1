# 技术文档管理工具

param(
    [string]$Action = "help",
    [string]$Topic = "",
    [string]$Keyword = ""
)

# 文档路径配置
$DocsPath = Get-Location
$IndexFile = "技术知识库索引.md"

function Show-Help {
    Write-Host "=== 技术文档管理工具 ===" -ForegroundColor Cyan
    Write-Host "用法：" -ForegroundColor Yellow
    Write-Host "  .\文档管理工具.ps1 -Action search -Keyword '关键词'" -ForegroundColor Gray
    Write-Host "  .\文档管理工具.ps1 -Action open -Topic 'Gemini'" -ForegroundColor Gray
    Write-Host "  .\文档管理工具.ps1 -Action backup" -ForegroundColor Gray
    Write-Host "  .\文档管理工具.ps1 -Action index" -ForegroundColor Gray
    Write-Host ""
    Write-Host "可用操作：" -ForegroundColor Yellow
    Write-Host "  search   - 搜索文档内容" -ForegroundColor Gray
    Write-Host "  open     - 打开相关文档" -ForegroundColor Gray
    Write-Host "  backup   - 创建备份" -ForegroundColor Gray
    Write-Host "  index    - 更新索引" -ForegroundColor Gray
    Write-Host "  list     - 列出所有文档" -ForegroundColor Gray
}

function Search-Docs {
    param([string]$SearchTerm)
    
    if ([string]::IsNullOrEmpty($SearchTerm)) {
        Write-Host "请提供搜索关键词" -ForegroundColor Red
        return
    }
    
    Write-Host "搜索关键词: $SearchTerm" -ForegroundColor Cyan
    Write-Host "搜索结果：" -ForegroundColor Yellow
    
    $Results = Select-String -Path "*.md" -Pattern $SearchTerm -Context 1
    
    if ($Results) {
        foreach ($Result in $Results) {
            Write-Host "文件: $($Result.Filename)" -ForegroundColor Green
            Write-Host "行号: $($Result.LineNumber)" -ForegroundColor Gray
            Write-Host "内容: $($Result.Line.Trim())" -ForegroundColor White
            Write-Host "---" -ForegroundColor DarkGray
        }
    } else {
        Write-Host "未找到相关内容" -ForegroundColor Red
    }
}

function Open-TopicDocs {
    param([string]$TopicName)
    
    $TopicMap = @{
        "gemini" = "Gemini-CLI-多账户配置指南.md"
        "powershell" = "Microsoft.PowerShell_profile.ps1"
        "对话" = "Gemini-CLI配置对话记录_完整版.md"
        "索引" = "技术知识库索引.md"
        "结构" = "文件组织结构说明.md"
    }
    
    $FileName = $TopicMap[$TopicName.ToLower()]
    
    if ($FileName -and (Test-Path $FileName)) {
        Write-Host "打开文档: $FileName" -ForegroundColor Green
        Start-Process notepad $FileName
    } else {
        Write-Host "未找到主题文档: $TopicName" -ForegroundColor Red
        Write-Host "可用主题: $($TopicMap.Keys -join ', ')" -ForegroundColor Yellow
    }
}

function Create-Backup {
    $Date = Get-Date -Format "yyyyMMdd_HHmm"
    $BackupDir = "备份_$Date"
    
    Write-Host "创建备份目录: $BackupDir" -ForegroundColor Cyan
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    
    # 备份所有 Markdown 文件
    Get-ChildItem "*.md" | ForEach-Object {
        Copy-Item $_.FullName $BackupDir
        Write-Host "已备份: $($_.Name)" -ForegroundColor Green
    }
    
    # 备份 PowerShell 文件
    Get-ChildItem "*.ps1" | ForEach-Object {
        Copy-Item $_.FullName $BackupDir
        Write-Host "已备份: $($_.Name)" -ForegroundColor Green
    }
    
    Write-Host "备份完成: $BackupDir" -ForegroundColor Yellow
}

function Update-Index {
    Write-Host "更新文档索引..." -ForegroundColor Cyan
    
    $IndexContent = @"
# 技术知识库索引 - 自动更新

**更新时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

## 📁 文档列表

"@
    
    Get-ChildItem "*.md" | ForEach-Object {
        $IndexContent += "- **$($_.BaseName)**: $($_.Name)`n"
    }
    
    $IndexContent += @"

## 🔧 配置文件

"@
    
    Get-ChildItem "*.ps1" | ForEach-Object {
        $IndexContent += "- **$($_.BaseName)**: $($_.Name)`n"
    }
    
    $IndexContent | Out-File "自动生成索引.md" -Encoding UTF8
    Write-Host "索引已更新: 自动生成索引.md" -ForegroundColor Green
}

function List-AllDocs {
    Write-Host "=== 所有技术文档 ===" -ForegroundColor Magenta
    
    Write-Host "`n📋 Markdown 文档:" -ForegroundColor Cyan
    Get-ChildItem "*.md" | ForEach-Object {
        $Size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "  $($_.Name) ($Size KB)" -ForegroundColor Gray
    }
    
    Write-Host "`n🔧 PowerShell 脚本:" -ForegroundColor Cyan
    Get-ChildItem "*.ps1" | ForEach-Object {
        $Size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "  $($_.Name) ($Size KB)" -ForegroundColor Gray
    }
    
    Write-Host "`n📊 统计信息:" -ForegroundColor Yellow
    $MdCount = (Get-ChildItem "*.md").Count
    $PsCount = (Get-ChildItem "*.ps1").Count
    Write-Host "  Markdown 文档: $MdCount 个" -ForegroundColor Gray
    Write-Host "  PowerShell 脚本: $PsCount 个" -ForegroundColor Gray
}

# 主逻辑
switch ($Action.ToLower()) {
    "search" { Search-Docs $Keyword }
    "open" { Open-TopicDocs $Topic }
    "backup" { Create-Backup }
    "index" { Update-Index }
    "list" { List-AllDocs }
    default { Show-Help }
}
