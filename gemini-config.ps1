# ===== Gemini CLI 多账号配置 =====

# 定义 API 密钥
$Global:GEMINI_KEYS = @{
    "key1" = "AIzaSyDSjvOlRqIIBOIXYX_Zj8jve__JBvIRj4E"
    "key2" = "AIzaSyBtMgLjj2etiUDEfl5-KDblhDfY0zxSpXU"
}

# 定义 Google Cloud 项目
$Global:GOOGLE_PROJECTS = @{
    "main" = "optimum-shore-439117-v9"
}

# 切换到 Google 账号模式
function Switch-ToGoogle {
    param([string]$ProjectName = "main")
    
    if ($Global:GOOGLE_PROJECTS.ContainsKey($ProjectName)) {
        $env:GOOGLE_CLOUD_PROJECT = $Global:GOOGLE_PROJECTS[$ProjectName]
        $env:GEMINI_API_KEY = $null
        Write-Host "✅ 已切换到 Google 账号模式: $ProjectName" -ForegroundColor Green
        Write-Host "📁 项目ID: $($Global:GOOGLE_PROJECTS[$ProjectName])" -ForegroundColor Gray
        Write-Host "💡 运行 gemini 后选择 'Login with Google'" -ForegroundColor Cyan
        Write-Host "📊 额度：60次/天，1000次/分钟" -ForegroundColor Yellow
    }
}

# 切换到 API 密钥模式
function Switch-ToApiKey {
    param([string]$KeyName = "key1")
    
    if ($Global:GEMINI_KEYS.ContainsKey($KeyName)) {
        $env:GOOGLE_CLOUD_PROJECT = $null
        $env:GEMINI_API_KEY = $Global:GEMINI_KEYS[$KeyName]
        Write-Host "✅ 已切换到 API 密钥模式: $KeyName" -ForegroundColor Green
        Write-Host "🔑 密钥: $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Yellow
        Write-Host "💡 运行 gemini 后选择 'Use Gemini API Key'" -ForegroundColor Cyan
        Write-Host "📊 额度：100次/天，15次/分钟" -ForegroundColor Yellow
        Write-Host "🌐 注意：需要代理连接" -ForegroundColor Red
    } else {
        Write-Host "❌ 未找到密钥: $KeyName" -ForegroundColor Red
        Write-Host "可用密钥: $($Global:GEMINI_KEYS.Keys -join ', ')" -ForegroundColor Cyan
    }
}

# 显示当前状态
function Show-GeminiStatus {
    Write-Host "=== Gemini 认证状态 ===" -ForegroundColor Magenta
    if ($env:GOOGLE_CLOUD_PROJECT) {
        Write-Host "🔵 当前模式: Google 账号" -ForegroundColor Blue
        Write-Host "📁 项目ID: $env:GOOGLE_CLOUD_PROJECT" -ForegroundColor Gray
    } elseif ($env:GEMINI_API_KEY) {
        Write-Host "🔑 当前模式: API 密钥" -ForegroundColor Green
        Write-Host "🔑 密钥: $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ 未配置认证方式" -ForegroundColor Red
    }
    Write-Host "========================" -ForegroundColor Magenta
}

# 保存对话上下文
function Save-GeminiContext {
    param([string]$Message = "保存当前对话上下文")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $contextFile = "GEMINI_CONTEXT.md"
    
    $contextContent = @"

## 上下文保存 - $timestamp
**原因**: $Message
**当前认证模式**: $(if ($env:GOOGLE_CLOUD_PROJECT) { "Google账号" } elseif ($env:GEMINI_API_KEY) { "API密钥" } else { "未配置" })

---
"@
    
    Add-Content -Path $contextFile -Value $contextContent
    Write-Host "✅ 上下文已保存到 $contextFile" -ForegroundColor Green
}

# 便捷函数
function gg { Switch-ToGoogle }
function gk1 { Switch-ToApiKey "key1" }
function gk2 { Switch-ToApiKey "key2" }
function gs { Show-GeminiStatus }
function gem-save { Save-GeminiContext }

# 启动提示
function Show-GeminiHelp {
    Write-Host "🎉 Gemini CLI 多账号管理已加载！" -ForegroundColor Green
    Write-Host "📋 可用命令：" -ForegroundColor Cyan
    Write-Host "  gs       - 显示当前认证状态" -ForegroundColor Gray
    Write-Host "  gg       - 切换到 Google 账号模式" -ForegroundColor Gray
    Write-Host "  gk1      - 切换到第一个 API 密钥" -ForegroundColor Gray
    Write-Host "  gk2      - 切换到第二个 API 密钥" -ForegroundColor Gray
    Write-Host "  gem-save - 保存当前对话上下文" -ForegroundColor Gray
    Write-Host "💡 使用 'Show-GeminiHelp' 再次显示此帮助" -ForegroundColor Yellow
}

# 默认设置为 Google 账号模式
Switch-ToGoogle

# 显示帮助信息
Show-GeminiHelp

# ===== Gemini CLI 配置结束 =====
