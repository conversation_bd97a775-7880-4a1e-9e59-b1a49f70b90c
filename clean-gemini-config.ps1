# Gemini CLI Clean Config

# API Keys
$Global:KEY1 = "AIzaSyDSjvOlRqIIBOIXYX_Zj8jve__JBvIRj4E"
$Global:KEY2 = "AIzaSyBtMgLjj2etiUDEfl5-KDblhDfY0zxSpXU"
$Global:PROJECT_ID = "optimum-shore-439117-v9"

# Switch to Google Account
function Global:gg {
    $env:GOOGLE_CLOUD_PROJECT = $Global:PROJECT_ID
    $env:GEMINI_API_KEY = $null
    Write-Host "Google Account Mode Activated" -ForegroundColor Green
    Write-Host "Project: $Global:PROJECT_ID" -ForegroundColor Gray
}

# Switch to API Key 1
function Global:gk1 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:KEY1
    Write-Host "API Key 1 Activated" -ForegroundColor Green
    Write-Host "Key: $($Global:KEY1.Substring(0,15))..." -ForegroundColor Yellow
}

# Switch to API Key 2
function Global:gk2 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:KEY2
    Write-Host "API Key 2 Activated" -ForegroundColor Green
    Write-Host "Key: $($Global:KEY2.Substring(0,15))..." -ForegroundColor Yellow
}

# Show Status
function Global:gs {
    Write-Host "=== Gemini Status ===" -ForegroundColor Magenta
    if ($env:GOOGLE_CLOUD_PROJECT) {
        Write-Host "Mode: Google Account" -ForegroundColor Blue
        Write-Host "Project: $env:GOOGLE_CLOUD_PROJECT" -ForegroundColor Gray
    } elseif ($env:GEMINI_API_KEY) {
        Write-Host "Mode: API Key" -ForegroundColor Green
        Write-Host "Key: $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Gray
    } else {
        Write-Host "Status: Not Configured" -ForegroundColor Red
    }
    Write-Host "====================" -ForegroundColor Magenta
}

# Save Context
function Global:gem-save {
    $time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $content = "Context saved at: $time"
    Add-Content -Path "GEMINI_CONTEXT.md" -Value $content
    Write-Host "Context saved successfully" -ForegroundColor Green
}

# Show Help
function Global:gem-help {
    Write-Host "Gemini CLI Loaded Successfully!" -ForegroundColor Green
    Write-Host "Available commands:" -ForegroundColor Cyan
    Write-Host "  gs       - Show status" -ForegroundColor Gray
    Write-Host "  gg       - Google account mode" -ForegroundColor Gray
    Write-Host "  gk1      - API key 1" -ForegroundColor Gray
    Write-Host "  gk2      - API key 2" -ForegroundColor Gray
    Write-Host "  gem-save - Save context" -ForegroundColor Gray
    Write-Host "  gem-help - Show this help" -ForegroundColor Gray
}

# Initialize
Write-Host "Loading Gemini CLI configuration..." -ForegroundColor Cyan
gg
gem-help
Write-Host "Ready to use! Try 'gs' to check status." -ForegroundColor Yellow
