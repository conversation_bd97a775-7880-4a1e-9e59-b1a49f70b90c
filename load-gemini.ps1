# Gemini CLI 全局加载器

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "🎯 Gemini CLI 全局加载器" -ForegroundColor Cyan
    Write-Host "用法：" -ForegroundColor Yellow
    Write-Host "  E:\mycode\wuzhuo\load-gemini.ps1" -ForegroundColor Gray
    Write-Host "  或者设置别名后使用：load-gemini" -ForegroundColor Gray
    Write-Host ""
    Write-Host "加载后可用命令：" -ForegroundColor Yellow
    Write-Host "  gs  - 显示状态" -ForegroundColor Gray
    Write-Host "  gg  - Google 账号模式" -ForegroundColor Gray
    Write-Host "  gk1 - API 密钥 1" -ForegroundColor Gray
    Write-Host "  gk2 - API 密钥 2" -ForegroundColor Gray
    return
}

Write-Host "🚀 正在从全局位置加载 Gemini CLI 配置..." -ForegroundColor Cyan

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# 加载全局配置
try {
    . "$ScriptDir\global-gemini-config.ps1"
    Write-Host "✅ 全局配置加载成功！" -ForegroundColor Green
} catch {
    Write-Host "❌ 配置加载失败：$($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 请确保文件 global-gemini-config.ps1 存在于：$ScriptDir" -ForegroundColor Yellow
}
