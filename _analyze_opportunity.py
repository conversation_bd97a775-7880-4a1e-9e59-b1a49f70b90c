import json
import os

def parse_value(s):
    """将'亿', '万', '%'等单位的字符串转换为数值"""
    s = str(s).strip()
    if not s:
        return 0
    if '%' in s:
        return float(s.replace('%', ''))
    s_lower = s.lower()
    if '亿' in s_lower:
        return float(s_lower.replace('亿', '')) * 1_0000_0000
    if '万' in s_lower:
        return float(s_lower.replace('万', '')) * 1_0000
    try:
        return float(s)
    except (ValueError, TypeError):
        return 0

def analyze_data(file_path):
    """分析股票数据文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"无法读取或解析文件: {e}")
        return

    all_results = []
    
    if 'dates' not in data or not isinstance(data['dates'], dict):
        print("JSON文件格式不正确，找不到 'dates' 对象。")
        return

    for date_str, daily_data in data['dates'].items():
        try:
            stock_list_data = daily_data.get('data', {}).get('data', {}).get('stock_list', {})
            head_info = stock_list_data.get('head_info', [])
            stock_list = stock_list_data.get('list', [])

            if not head_info or not stock_list:
                continue

            # 创建表头到索引的映射
            header_map = {item['text']: i for i, item in enumerate(head_info)}
            
            required_keys = ['股票', '竞价成交', '竞价涨幅', '最终额', '涨幅']
            if not all(key in header_map for key in required_keys):
                # print(f"日期 {date_str} 的数据缺少必要的列，跳过。")
                continue

            for stock_data in stock_list:
                try:
                    # 提取数据
                    jj_turnover_val = parse_value(stock_data[header_map['竞价成交']].get('rank', 0))
                    jj_percent_val = parse_value(stock_data[header_map['竞价涨幅']].get('rank', 0)) * 100
                    final_amount_val = parse_value(stock_data[header_map['最终额']].get('rank', 0))
                    day_performance_val = parse_value(stock_data[header_map['涨幅']].get('rank', 0)) * 100

                    # 应用筛选条件
                    if final_amount_val > 30_000_000 and -2 <= jj_percent_val <= 4:
                        result = {
                            'date': date_str,
                            'name': stock_data[header_map['股票']].get('text', 'N/A'),
                            'jj_turnover': stock_data[header_map['竞价成交']].get('text', 'N/A'),
                            'jj_turnover_val': jj_turnover_val,
                            'jj_percent': stock_data[header_map['竞价涨幅']].get('text', 'N/A'),
                            'final_amount': stock_data[header_map['最终额']].get('text', 'N/A'),
                            'day_performance': stock_data[header_map['涨幅']].get('text', 'N/A')
                        }
                        all_results.append(result)
                except (IndexError, KeyError, TypeError):
                    # 忽略单个股票的数据提取错误
                    continue
        except (KeyError, TypeError):
            # 忽略单个日期的数据结构问题
            continue
            
    # 按竞价成交额从大到小排序
    all_results.sort(key=lambda x: x['jj_turnover_val'], reverse=True)

    # 打印结果
    if not all_results:
        print("在指定日期范围内，没有找到符合所有条件的股票。")
        return

    print(f"{'日期':<12}{'股票名称':<15}{'竞价成交额':<12}{'竞价涨幅':<10}{'最终封单额':<12}{'当日表现':<10}")
    print("-" * 71)
    for res in all_results:
        print(f"{res['date']:<12}{res['name']:<15}{res['jj_turnover']:<12}{res['jj_percent']:<10}{res['final_amount']:<12}{res['day_performance']:<10}")

if __name__ == "__main__":
    # Correct the file path for Windows
    file_path = os.path.join('E:\\', 'mycode', 'wuzhuo', '数据分析测试', '涨停抢筹_所有历史数据.json')
    analyze_data(file_path)
