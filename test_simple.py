#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Python测试程序
"""
import datetime
import random

def hello(name="World"):
    """返回个性化问候语"""
    return f"Hello, {name}!"

def add(a, b):
    """简单的加法函数"""
    return a + b

def multiply(a, b):
    """乘法函数"""
    return a * b

def get_current_time():
    """获取当前时间"""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def generate_random_number(min_val=1, max_val=100):
    """生成随机数"""
    return random.randint(min_val, max_val)

def calculate_factorial(n):
    """计算阶乘"""
    if n < 0:
        return "负数没有阶乘"
    elif n == 0 or n == 1:
        return 1
    else:
        result = 1
        for i in range(2, n + 1):
            result *= i
        return result

def is_prime(n):
    """判断是否为质数"""
    if n < 2:
        return False
    for i in range(2, int(n ** 0.5) + 1):
        if n % i == 0:
            return False
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("🐍 Python 测试程序 - 增强版")
    print("=" * 50)

    # 基础功能测试
    print(f"1. 问候: {hello()}")
    print(f"2. 个性化问候: {hello('张三')}")
    print(f"3. 加法: 2 + 3 = {add(2, 3)}")
    print(f"4. 乘法: 4 × 5 = {multiply(4, 5)}")

    # 时间和随机数
    print(f"5. 当前时间: {get_current_time()}")
    random_num = generate_random_number()
    print(f"6. 随机数 (1-100): {random_num}")

    # 数学计算
    print(f"7. 5的阶乘: {calculate_factorial(5)}")
    print(f"8. {random_num} 是质数吗? {is_prime(random_num)}")

    # 列表操作
    numbers = [1, 2, 3, 4, 5]
    print(f"9. 数字列表: {numbers}")
    print(f"10. 列表求和: {sum(numbers)}")
    print(f"11. 列表平均值: {sum(numbers) / len(numbers):.2f}")

    print("=" * 50)
    print("✅ 所有测试完成！")
    print("=" * 50)
