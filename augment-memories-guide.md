# Augment Memories 使用指南

## 🧠 什么是 Augment Memories

Augment Memories 是 Augment Code 的智能记忆系统，能够：
- 自动记录您的技术偏好和决策
- 学习您的编程习惯和工作流程
- 在后续对话中自动应用这些记忆
- 提供个性化的代码建议

## 📍 访问 Augment Memories

### 方法一：通过聊天界面
1. 在 Augment Chat 输入框附近找到 "Augment Memories" 按钮
2. 点击查看已保存的记忆内容
3. 可以编辑、删除或添加新的记忆

### 方法二：通过上下文菜单
1. 点击 `@` 符号（上下文菜单）
2. 选择 "Augment Memories"
3. 浏览和管理现有记忆

## 🔄 Memories 自动记录的内容

### 技术栈偏好
- "我更喜欢使用 React 而不是 Vue"
- "我们项目统一使用 TypeScript"
- "我习惯使用 Tailwind CSS 进行样式开发"

### 编程习惯
- "我喜欢函数式编程风格"
- "我习惯使用 async/await 而不是 Promise.then"
- "我更喜欢使用 const 和 let，避免使用 var"

### 工具和配置
- "我们使用 Jest 进行单元测试"
- "我们的项目使用 ESLint + Prettier 进行代码格式化"
- "我们使用 Vite 作为构建工具"

### 架构决策
- "我们采用微服务架构"
- "我们使用 RESTful API 设计"
- "我们遵循 MVC 设计模式"

## 📝 手动创建和管理 Memories

### 主动创建记忆
您可以通过以下方式主动创建记忆：

```
请记住：我们公司的标准技术栈是 Vue 3 + TypeScript + Vite + Element Plus
```

```
请记住：我们的代码规范要求所有函数都要有中文注释
```

```
请记住：我们使用 Git Flow 工作流，所有功能开发都在 feature 分支进行
```

### 编辑现有记忆
1. 打开 Augment Memories
2. 找到要编辑的记忆条目
3. 点击编辑按钮
4. 修改内容并保存

### 删除不需要的记忆
1. 在 Memories 列表中找到要删除的条目
2. 点击删除按钮
3. 确认删除

## 🔄 将 Memories 转换为规则

### 转换为用户指南
1. 在 Augment Memories 中选择要转换的记忆
2. 点击 "User Guidelines" 按钮
3. 将记忆内容添加到用户指南中

### 转换为项目规则
1. 选择相关的记忆内容
2. 在设置中创建新的规则文件
3. 将记忆内容整理成规则格式

## 💡 最佳实践

### 记忆内容建议
- **具体而非抽象**：记录具体的技术选择和配置
- **有上下文**：说明为什么做出这样的决策
- **可操作**：记录的内容应该能指导具体的开发行为

### 示例记忆内容
```
技术栈记忆：
- 前端：Vue 3 + TypeScript + Vite + Element Plus
- 后端：Node.js + Express + MongoDB
- 测试：Jest + Vue Test Utils
- 部署：Docker + Nginx

编码规范记忆：
- 所有函数必须有中文注释
- 使用 ESLint + Prettier 进行代码格式化
- 变量命名使用驼峰命名法
- 常量使用大写字母和下划线

工作流程记忆：
- 使用 Git Flow 工作流
- 所有 PR 必须经过代码审查
- 提交信息遵循 Conventional Commits 规范
- 每个功能都要有对应的单元测试
```

## 🔍 查看和搜索 Memories

### 查看所有记忆
1. 打开 Augment Memories
2. 浏览完整的记忆列表
3. 按类别或时间排序查看

### 搜索特定记忆
1. 在 Memories 界面使用搜索功能
2. 输入关键词查找相关记忆
3. 快速定位需要的信息

## 🚀 高级用法

### 项目特定记忆
- 为不同项目创建不同的记忆集合
- 根据项目类型调整技术栈偏好
- 记录项目特有的架构决策

### 团队协作记忆
- 记录团队共同的编码规范
- 保存团队使用的工具和流程
- 记录团队的最佳实践

### 学习和成长记忆
- 记录新学到的技术和方法
- 保存解决问题的经验
- 记录性能优化的技巧

## 📊 Memories 的价值

### 提高效率
- 减少重复配置和说明
- 自动应用个人偏好
- 快速获得个性化建议

### 保持一致性
- 确保代码风格统一
- 维护技术栈一致性
- 遵循既定的最佳实践

### 知识积累
- 保存重要的技术决策
- 积累解决问题的经验
- 建立个人知识库
