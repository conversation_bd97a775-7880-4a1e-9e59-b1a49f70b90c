# Gemini CLI 多账户配置 - 完整对话记录

**对话时间**: 2024年12月19日  
**参与者**: 用户 & Augment Agent  
**主题**: Gemini CLI 多账户配置与故障排除  

---

## 📋 对话概要

本次对话解决了 Gemini CLI 多账户配置的完整方案，包括：
- Google 账号认证配置
- 多 API 密钥管理
- PowerShell 永久配置
- 故障排除和优化

---

## 🔧 核心技术方案

### 1. 问题背景
- **需求**: 解决 Gemini CLI 单账户额度限制问题
- **目标**: 实现多账户无缝切换，一次配置永久使用
- **挑战**: 环境变量配置、跨目录使用、重启后失效

### 2. 最终解决方案

#### 永久 PowerShell 配置文件
**文件位置**: `C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1`

**核心配置代码**:
```powershell
# Gemini CLI 配置
$Global:GEMINI_KEY1 = "AIzaSyDSjvOlRqIIBOIXYX_Zj8jve__JBvIRj4E"
$Global:GEMINI_KEY2 = "AIzaSyBtMgLjj2etiUDEfl5-KDblhDfY0zxSpXU"
$Global:GEMINI_PROJECT = "optimum-shore-439117-v9"

# 核心函数
function Global:gg { 
    $env:GOOGLE_CLOUD_PROJECT = $Global:GEMINI_PROJECT
    $env:GEMINI_API_KEY = $null
}

function Global:gk1 { 
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY1
}

function Global:gk2 { 
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:GEMINI_KEY2
}

function Global:gs { 
    # 显示当前认证状态
}

function Global:gem {
    param([string]$Mode = "google")
    switch ($Mode.ToLower()) {
        "google" { gg; gemini }
        "api1" { gk1; gemini }
        "api2" { gk2; gemini }
    }
}
```

### 3. 关键命令总结

| 命令 | 功能 | 使用场景 |
|------|------|----------|
| `gem` | 快速启动（Google账号） | 日常使用 |
| `gem api1` | 快速启动（API密钥1） | Google额度用完 |
| `gem api2` | 快速启动（API密钥2） | 第一个API用完 |
| `gs` | 显示认证状态 | 检查当前配置 |
| `gg` | 切换Google账号 | 手动切换 |
| `gk1/gk2` | 切换API密钥 | 手动切换 |
| `gem-save` | 保存对话上下文 | 切换前保存 |

---

## 🚨 故障排除记录

### 问题1: PowerShell 语法错误
**现象**: 日期格式和 here-string 语法错误
**解决**: 简化语法，避免复杂的字符串处理

### 问题2: 字符编码问题
**现象**: 中文字符导致解析错误
**解决**: 使用纯英文版本，避免编码冲突

### 问题3: API 密钥地区限制
**现象**: "User location is not supported"
**解决**: 使用代理连接到支持地区

### 问题4: 跨目录使用问题
**现象**: 在不同目录下命令不可用
**解决**: 创建全局 PowerShell 配置文件

---

## 📊 配置演进过程

1. **初始方案**: 临时环境变量设置
2. **改进方案**: 项目级配置文件
3. **优化方案**: 全局配置脚本
4. **最终方案**: 永久 PowerShell 配置文件

---

## 🎯 最佳实践总结

### 认证方式选择
- **优先使用**: Google 账号（60次/天，1000次/分钟）
- **备用方案**: API 密钥（100次/天，15次/分钟）
- **多账户策略**: 2-3个 API 密钥轮换使用

### 使用流程
1. 日常使用 `gem` 启动
2. 额度不足时 `gem-save` 保存上下文
3. 切换到 `gem api1` 或 `gem api2`
4. 在新会话中恢复上下文

### 配置管理
- 定期备份配置文件
- 使用版本控制管理配置变更
- 为不同项目创建专用配置

---

## 📁 相关文件清单

1. **主配置文件**: `Microsoft.PowerShell_profile.ps1`
2. **技术文档**: `Gemini-CLI-多账户配置指南.md`
3. **对话记录**: `Gemini-CLI配置对话记录_完整版.md`
4. **备用配置**: `clean-gemini-config.ps1`
5. **上下文文件**: `GEMINI_CONTEXT.md`

---

## 🔄 后续优化方向

1. **自动化**: 创建额度监控和自动切换脚本
2. **集成**: 与 VS Code 扩展集成
3. **管理**: 开发配置管理界面
4. **监控**: 添加使用统计和报告功能

---

## 💡 经验教训

1. **配置持久化**: 系统级配置比项目级配置更可靠
2. **错误处理**: 充分的错误处理和用户提示很重要
3. **文档化**: 详细的文档是成功实施的关键
4. **测试验证**: 在不同环境下充分测试配置

---

*记录创建时间: 2024年12月19日*  
*最后更新: 2024年12月19日*
