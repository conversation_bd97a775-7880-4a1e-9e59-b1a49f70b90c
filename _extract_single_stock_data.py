

import json
import os

def find_stock_data(file_path, target_date, stock_name):
    """
    在给定的JSON数据文件中查找特定日期和特定股票的原始数据。
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误: 无法读取或解析文件: {e}")
        return

    # 定位到指定日期的数据
    daily_data = data.get("dates", {}).get(target_date)
    if not daily_data:
        print(f"错误: 在文件中没有找到日期 {target_date} 的数据。")
        return

    try:
        stock_list_data = daily_data.get('data', {}).get('data', {}).get('stock_list', {})
        head_info = stock_list_data.get('head_info', [])
        stock_list = stock_list_data.get('list', [])

        if not head_info or not stock_list:
            print(f"错误: 日期 {target_date} 的数据不完整，缺少表头或股票列表。")
            return

        # 查找目标股票
        found_stock = None
        for stock_entry in stock_list:
            # 股票名称在第一个元素里
            if stock_name in stock_entry[0].get("text", ""):
                found_stock = stock_entry
                break
        
        if not found_stock:
            print(f"在日期 {target_date} 未找到股票: {stock_name}")
            return

        # 打印结果
        print(f"--- {target_date} 股票: {stock_name} 的原始数据 ---")
        
        print("\n--- 表头 (Head Info) ---")
        header_texts = [h.get('text', 'N/A') for h in head_info]
        print(header_texts)

        print(f"\n--- {stock_name} 的数据条目 (Data Entry) ---")
        # 使用json.dumps美化输出，确保中文正常显示
        print(json.dumps(found_stock, indent=2, ensure_ascii=False))

    except (KeyError, IndexError, TypeError) as e:
        print(f"处理数据时发生错误: {e}")

if __name__ == "__main__":
    file_path = os.path.join('E:\\', 'mycode', 'wuzhuo', '数据分析测试', '涨停抢筹_所有历史数据.json')
    target_date = "2025-06-27"
    stock_name = "天风证券"
    find_stock_data(file_path, target_date, stock_name)

