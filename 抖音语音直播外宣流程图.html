<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音语音直播外宣工作流程与AI提效方案</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
            background: white;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>抖音语音直播外宣工作流程与AI提效方案</h1>
        
        <div class="controls">
            <button onclick="zoomIn()">放大</button>
            <button onclick="zoomOut()">缩小</button>
            <button onclick="resetZoom()">重置</button>
            <button onclick="downloadSVG()">下载图片</button>
        </div>
        
        <div class="mermaid" id="mermaidDiagram">
flowchart TD
    A[开始外宣活动] --> B[目标用户分析]
    B --> B1[用户画像分析]
    B --> B2[竞品分析]
    B --> B3[市场趋势研究]
    
    B1 --> C[内容策划]
    B2 --> C
    B3 --> C
    
    C --> C1[文案创作]
    C --> C2[视觉设计]
    C --> C3[音频制作]
    C --> C4[直播话术设计]
    
    C1 --> D[渠道投放]
    C2 --> D
    C3 --> D
    C4 --> D
    
    D --> D1[社交媒体投放]
    D --> D2[KOL合作推广]
    D --> D3[付费广告投放]
    D --> D4[社群运营]
    D --> D5[线下推广]
    
    D1 --> E[数据监控]
    D2 --> E
    D3 --> E
    D4 --> E
    D5 --> E
    
    E --> E1[流量数据分析]
    E --> E2[转化率监控]
    E --> E3[用户行为分析]
    E --> E4[ROI计算]
    
    E1 --> F[效果优化]
    E2 --> F
    E3 --> F
    E4 --> F
    
    F --> F1[内容调整]
    F --> F2[渠道优化]
    F --> F3[投放策略调整]
    
    F1 --> G[转化跟进]
    F2 --> G
    F3 --> G
    
    G --> G1[潜在用户沟通]
    G --> G2[试播安排]
    G --> G3[签约谈判]
    G --> G4[培训指导]
    
    G1 --> H[用户维护]
    G2 --> H
    G3 --> H
    G4 --> H
    
    H --> H1[定期回访]
    H --> H2[活动邀请]
    H --> H3[收益分析]
    H --> H4[续约管理]
    
    H1 --> I[效果评估]
    H2 --> I
    H3 --> I
    H4 --> I
    
    I --> J{是否达到目标}
    J -->|是| K[活动结束]
    J -->|否| F
    
    %% AI提效标注
    B1 -.->|AI提效| AI1[AI用户画像分析<br/>自动数据挖掘]
    B2 -.->|AI提效| AI2[AI竞品监控<br/>自动化分析报告]
    C1 -.->|AI提效| AI3[AI文案生成<br/>多版本A/B测试]
    C2 -.->|AI提效| AI4[AI设计生成<br/>批量素材制作]
    C3 -.->|AI提效| AI5[AI语音合成<br/>多语言版本]
    E1 -.->|AI提效| AI6[AI数据分析<br/>实时监控预警]
    E3 -.->|AI提效| AI7[AI行为分析<br/>用户意图识别]
    F1 -.->|AI提效| AI8[AI内容优化<br/>自动化调整]
    G1 -.->|AI提效| AI9[AI客服机器人<br/>24小时响应]
    H1 -.->|AI提效| AI10[AI客户管理<br/>智能提醒跟进]
    
    %% 样式设置
    classDef aiNode fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processNode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decisionNode fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class AI1,AI2,AI3,AI4,AI5,AI6,AI7,AI8,AI9,AI10 aiNode
    class A,B,C,D,E,F,G,H,I,K processNode
    class J decisionNode
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        let currentZoom = 1;
        
        function zoomIn() {
            currentZoom += 0.2;
            document.querySelector('.mermaid svg').style.transform = `scale(${currentZoom})`;
        }
        
        function zoomOut() {
            currentZoom = Math.max(0.2, currentZoom - 0.2);
            document.querySelector('.mermaid svg').style.transform = `scale(${currentZoom})`;
        }
        
        function resetZoom() {
            currentZoom = 1;
            document.querySelector('.mermaid svg').style.transform = `scale(${currentZoom})`;
        }
        
        function downloadSVG() {
            const svg = document.querySelector('.mermaid svg');
            const svgData = new XMLSerializer().serializeToString(svg);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const link = document.createElement('a');
                link.download = '抖音语音直播外宣流程图.png';
                link.href = canvas.toDataURL();
                link.click();
            };
            
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
    </script>
</body>
</html>
