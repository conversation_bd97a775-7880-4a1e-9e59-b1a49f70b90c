# Gemini CLI 简化配置

# API 密钥
$Global:KEY1 = "AIzaSyDSjvOlRqIIBOIXYX_Zj8jve__JBvIRj4E"
$Global:KEY2 = "AIzaSyBtMgLjj2etiUDEfl5-KDblhDfY0zxSpXU"
$Global:PROJECT_ID = "optimum-shore-439117-v9"

# 切换到 Google 账号
function Global:gg {
    $env:GOOGLE_CLOUD_PROJECT = $Global:PROJECT_ID
    $env:GEMINI_API_KEY = $null
    Write-Host "✅ Google 账号模式" -ForegroundColor Green
    Write-Host "📁 项目: $Global:PROJECT_ID" -ForegroundColor Gray
}

# 切换到第一个 API 密钥
function Global:gk1 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:KEY1
    Write-Host "✅ API 密钥 1" -ForegroundColor Green
    Write-Host "🔑 密钥: $($Global:KEY1.Substring(0,15))..." -ForegroundColor Yellow
}

# 切换到第二个 API 密钥
function Global:gk2 {
    $env:GOOGLE_CLOUD_PROJECT = $null
    $env:GEMINI_API_KEY = $Global:KEY2
    Write-Host "✅ API 密钥 2" -ForegroundColor Green
    Write-Host "🔑 密钥: $($Global:KEY2.Substring(0,15))..." -ForegroundColor Yellow
}

# 显示状态
function Global:gs {
    Write-Host "=== Gemini 状态 ===" -ForegroundColor Magenta
    if ($env:GOOGLE_CLOUD_PROJECT) {
        Write-Host "🔵 Google 账号模式" -ForegroundColor Blue
        Write-Host "📁 $env:GOOGLE_CLOUD_PROJECT" -ForegroundColor Gray
    } elseif ($env:GEMINI_API_KEY) {
        Write-Host "🔑 API 密钥模式" -ForegroundColor Green
        Write-Host "🔑 $($env:GEMINI_API_KEY.Substring(0,15))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ 未配置" -ForegroundColor Red
    }
    Write-Host "==================" -ForegroundColor Magenta
}

# 保存上下文
function Global:gem-save {
    $time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $content = "`n## 保存时间: $time`n"
    Add-Content -Path "GEMINI_CONTEXT.md" -Value $content
    Write-Host "✅ 已保存上下文" -ForegroundColor Green
}

# 显示帮助
function Global:gem-help {
    Write-Host "🎉 Gemini CLI 已加载！" -ForegroundColor Green
    Write-Host "命令：gs, gg, gk1, gk2, gem-save" -ForegroundColor Cyan
}

# 初始化
gg
gem-help
