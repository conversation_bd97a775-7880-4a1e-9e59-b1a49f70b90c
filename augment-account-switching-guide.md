# Augment Code 账号切换数据保护指南

## 🔄 账号切换影响分析

### 会丢失的数据（账号绑定）
- ❌ 会话历史记录
- ❌ Augment Memories
- ❌ User Guidelines
- ❌ 个人偏好设置
- ❌ 聊天上下文

### 不会丢失的数据（项目绑定）
- ✅ 工作空间代码索引（重新同步后恢复）
- ✅ 项目级 Workspace Guidelines (.augment-guidelines)
- ✅ 项目级 Rules 文件 (.augment/rules/)
- ✅ 本地项目配置

## 🛡️ 切换前的数据备份策略

### 1. 导出重要会话记录
```markdown
# 重要技术讨论备份 - {日期}

## 会话主题：{主题}
## 参与账号：{账号信息}
## 关键决策：
- 决策1：具体内容
- 决策2：具体内容

## 技术方案：
- 方案描述
- 实现细节
- 注意事项

## 后续行动：
- [ ] 待办事项1
- [ ] 待办事项2
```

### 2. 备份 Augment Memories
在切换前，将重要的 Memories 转换为文档：

```markdown
# Augment Memories 备份 - {日期}

## 技术栈偏好
- 前端：Vue 3 + TypeScript + Vite
- 后端：Node.js + Express
- 数据库：MongoDB

## 编码规范
- 代码注释使用中文
- 遵循 ESLint + Prettier 规范
- Git 提交信息遵循 Conventional Commits

## 工作流程
- 使用 Git Flow 工作流
- 所有 PR 需要代码审查
- 重要更新需要创建 CHANGELOG

## 环境配置
- Windows 10 开发环境
- 考虑中国网络环境
- 使用 PowerShell 进行配置管理
```

### 3. 备份 User Guidelines
```markdown
# User Guidelines 备份 - {日期}

## 基础规则
- 请始终保持中文交流
- 在重要技术讨论后主动询问是否需要保存对话记录

## Git 提交规则
- 首要目的是开发者工作上下文
- 必须包含：conventional commits格式标题
- 详细变更说明（每个修改点具体描述）
- Bug修复记录（问题描述、根因、修复方法）
- 版本号建议

## 开发规范
- 代码注释使用中文
- 考虑Windows环境和中国网络环境
- 重要技术决策形成文档记录
```

## 🔄 切换后的数据恢复策略

### 1. 重新配置 User Guidelines
使用备份的内容重新设置：
1. 打开 Augment Chat
2. 点击 @ 符号
3. 选择 "User Guidelines"
4. 粘贴备份的规则内容
5. 保存设置

### 2. 重新创建 Memories
根据备份内容重新创建记忆：
```
请记住：我偏好使用中文交流，在Windows环境开发，需要考虑中国网络环境
```

### 3. 重新同步工作空间
1. 打开项目
2. 等待 Augment 提示同步
3. 点击 "Sync" 按钮
4. 等待代码索引重建完成

### 4. 验证配置
- 测试 User Guidelines 是否生效
- 检查 Memories 是否正确
- 确认工作空间索引完整

## 📋 最佳实践建议

### 定期备份策略
- 每月备份一次重要的 Memories 和 Guidelines
- 重要技术讨论后立即保存到项目文档
- 使用版本控制管理配置文件

### 项目级配置优先
- 重要的规则放在项目级别（.augment-guidelines）
- 团队共享的配置使用 Rules 文件
- 个人偏好使用 User Guidelines

### 多账号管理
- 为不同账号维护相同的基础配置
- 使用脚本自动化配置恢复
- 建立配置同步机制

## 🚨 注意事项

### 数据安全
- 备份文件不要包含敏感信息
- 使用版本控制时注意隐私保护
- 定期清理过期的备份文件

### 账号管理
- 避免频繁切换账号
- 建立清晰的账号使用规则
- 记录每个账号的用途和配置

### 团队协作
- 团队成员使用统一的配置标准
- 重要配置变更要通知团队
- 建立配置文档和培训机制

## 🔧 自动化脚本示例

### PowerShell 配置备份脚本
```powershell
# Augment 配置备份脚本
$BackupDir = "E:\mycode\augment-backups"
$Date = Get-Date -Format "yyyy-MM-dd"

# 创建备份目录
New-Item -ItemType Directory -Path "$BackupDir\$Date" -Force

# 备份提示
Write-Host "请手动导出以下内容到备份目录：" -ForegroundColor Yellow
Write-Host "1. Augment Memories" -ForegroundColor Cyan
Write-Host "2. User Guidelines" -ForegroundColor Cyan
Write-Host "3. 重要会话记录" -ForegroundColor Cyan

# 打开备份目录
Start-Process $BackupDir
```

### 配置恢复检查清单
```markdown
## 账号切换后恢复检查清单

- [ ] 重新登录新账号
- [ ] 重新同步工作空间
- [ ] 恢复 User Guidelines
- [ ] 重新创建 Memories
- [ ] 测试配置是否生效
- [ ] 验证代码索引完整性
- [ ] 检查项目级配置
- [ ] 更新团队文档
```
