# 增强版A股趋势分析工具
# 创建日期：2025-07-26
# 功能：提供更全面的技术分析指标

import json
import os
import math
from datetime import datetime, timedelta
from collections import defaultdict

def parse_value(s):
    """将'亿', '万', '%'等单位的字符串转换为数值"""
    s = str(s).strip()
    if not s or s == 'N/A':
        return 0
    if '%' in s:
        return float(s.replace('%', ''))
    s_lower = s.lower()
    if '亿' in s_lower:
        return float(s_lower.replace('亿', '')) * 100000000
    if '万' in s_lower:
        return float(s_lower.replace('万', '')) * 10000
    try:
        return float(s)
    except (ValueError, TypeError):
        return 0

def calculate_rsi(prices, period=14):
    """计算RSI相对强弱指标"""
    if len(prices) < period + 1:
        return None
    
    gains = []
    losses = []
    
    for i in range(1, len(prices)):
        change = prices[i] - prices[i-1]
        if change > 0:
            gains.append(change)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(abs(change))
    
    if len(gains) < period:
        return None
    
    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period
    
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    if len(prices) < slow:
        return None, None, None
    
    # 计算EMA
    def ema(data, period):
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]
        for i in range(1, len(data)):
            ema_values.append((data[i] * multiplier) + (ema_values[-1] * (1 - multiplier)))
        return ema_values
    
    fast_ema = ema(prices, fast)
    slow_ema = ema(prices, slow)
    
    # 计算MACD线
    macd_line = []
    for i in range(len(slow_ema)):
        if i < len(fast_ema):
            macd_line.append(fast_ema[i] - slow_ema[i])
    
    if len(macd_line) < signal:
        return None, None, None
    
    # 计算信号线
    signal_line = ema(macd_line, signal)
    
    # 计算柱状图
    histogram = []
    for i in range(len(signal_line)):
        if i < len(macd_line):
            histogram.append(macd_line[i] - signal_line[i])
    
    return macd_line[-1], signal_line[-1], histogram[-1]

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带"""
    if len(prices) < period:
        return None, None, None
    
    recent_prices = prices[-period:]
    sma = sum(recent_prices) / period
    
    variance = sum((price - sma) ** 2 for price in recent_prices) / period
    std = math.sqrt(variance)
    
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    
    return upper_band, sma, lower_band

def analyze_technical_indicators(stock_data):
    """分析技术指标"""
    daily_data = stock_data['daily_data']
    if len(daily_data) < 10:
        return {}
    
    # 构建价格序列（使用累计收益率模拟）
    prices = []
    cumulative = 100  # 基准价格
    for d in daily_data:
        cumulative *= (1 + d['daily_return'] / 100)
        prices.append(cumulative)
    
    indicators = {}
    
    # RSI
    rsi = calculate_rsi(prices)
    if rsi:
        indicators['RSI'] = rsi
        if rsi > 70:
            indicators['RSI_signal'] = '超买'
        elif rsi < 30:
            indicators['RSI_signal'] = '超卖'
        else:
            indicators['RSI_signal'] = '正常'
    
    # MACD
    macd, signal, histogram = calculate_macd(prices)
    if macd and signal:
        indicators['MACD'] = macd
        indicators['MACD_signal'] = signal
        indicators['MACD_histogram'] = histogram
        if macd > signal:
            indicators['MACD_trend'] = '多头'
        else:
            indicators['MACD_trend'] = '空头'
    
    # 布林带
    upper, middle, lower = calculate_bollinger_bands(prices)
    if upper and middle and lower:
        current_price = prices[-1]
        indicators['BB_upper'] = upper
        indicators['BB_middle'] = middle
        indicators['BB_lower'] = lower
        
        if current_price > upper:
            indicators['BB_position'] = '上轨上方'
        elif current_price < lower:
            indicators['BB_position'] = '下轨下方'
        else:
            indicators['BB_position'] = '轨道内'
    
    return indicators

def calculate_volatility(daily_data):
    """计算波动率"""
    if len(daily_data) < 5:
        return 0
    
    returns = [d['daily_return'] for d in daily_data]
    mean_return = sum(returns) / len(returns)
    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
    volatility = math.sqrt(variance)
    
    return volatility

def analyze_sector_performance(stock_performance):
    """分析板块表现（基于股票名称推测）"""
    sector_mapping = {
        '军工': ['军工', '航天', '航空', '兵器'],
        '医疗': ['医疗', '医药', '生物', '健康'],
        '能源': ['能源', '石油', '煤炭', '电力'],
        '科技': ['科技', '软件', '电子', '通信'],
        '金融': ['银行', '证券', '保险', '信托'],
        '地产': ['地产', '房地产', '建筑'],
        '消费': ['消费', '食品', '饮料', '零售'],
        '制造': ['制造', '机械', '汽车', '钢铁']
    }
    
    sector_stocks = defaultdict(list)
    
    for stock_name, data in stock_performance.items():
        for sector, keywords in sector_mapping.items():
            if any(keyword in stock_name for keyword in keywords):
                sector_stocks[sector].append({
                    'name': stock_name,
                    'return': sum(d['daily_return'] for d in data)
                })
                break
        else:
            sector_stocks['其他'].append({
                'name': stock_name,
                'return': sum(d['daily_return'] for d in data)
            })
    
    # 计算各板块平均表现
    sector_performance = {}
    for sector, stocks in sector_stocks.items():
        if stocks:
            avg_return = sum(s['return'] for s in stocks) / len(stocks)
            sector_performance[sector] = {
                'avg_return': avg_return,
                'stock_count': len(stocks),
                'best_stock': max(stocks, key=lambda x: x['return'])
            }
    
    return sector_performance

def generate_comprehensive_report(trend_analysis, stock_performance):
    """生成综合分析报告"""
    print(f"\n{'='*80}")
    print("🔬 综合技术分析报告")
    print(f"{'='*80}")
    
    # 技术指标分析
    print(f"\n📊 技术指标分析 (TOP 5):")
    print(f"{'股票':<15}{'RSI':<8}{'RSI信号':<8}{'MACD趋势':<8}{'布林带位置':<12}{'波动率':<8}")
    print("-" * 70)
    
    for i, stock in enumerate(trend_analysis[:5], 1):
        indicators = analyze_technical_indicators(stock)
        volatility = calculate_volatility(stock['daily_data'])
        
        rsi = f"{indicators.get('RSI', 0):.1f}" if indicators.get('RSI') else "N/A"
        rsi_signal = indicators.get('RSI_signal', 'N/A')
        macd_trend = indicators.get('MACD_trend', 'N/A')
        bb_position = indicators.get('BB_position', 'N/A')
        
        print(f"{stock['stock_name']:<15}{rsi:<8}{rsi_signal:<8}{macd_trend:<8}{bb_position:<12}{volatility:.2f}%")
    
    # 板块分析
    sector_perf = analyze_sector_performance(stock_performance)
    if sector_perf:
        print(f"\n🏭 板块表现分析:")
        sorted_sectors = sorted(sector_perf.items(), key=lambda x: x[1]['avg_return'], reverse=True)
        
        print(f"{'板块':<10}{'平均涨幅':<10}{'股票数量':<8}{'最佳股票':<15}{'最佳涨幅':<10}")
        print("-" * 60)
        
        for sector, data in sorted_sectors:
            print(f"{sector:<10}{data['avg_return']:.2f}%{data['stock_count']:<8}{data['best_stock']['name']:<15}{data['best_stock']['return']:.2f}%")
    
    # 风险评估
    print(f"\n⚠️ 风险评估:")
    high_vol_stocks = [s for s in trend_analysis if calculate_volatility(s['daily_data']) > 2.0]
    low_vol_stocks = [s for s in trend_analysis if calculate_volatility(s['daily_data']) < 0.5]
    
    print(f"  高波动股票 (波动率>2%): {len(high_vol_stocks)}只")
    print(f"  低波动股票 (波动率<0.5%): {len(low_vol_stocks)}只")
    
    if high_vol_stocks:
        print(f"  高风险股票: {', '.join([s['stock_name'] for s in high_vol_stocks[:3]])}")
    
    if low_vol_stocks:
        print(f"  低风险股票: {', '.join([s['stock_name'] for s in low_vol_stocks[:3]])}")

def main():
    """主函数"""
    print("🚀 增强版A股趋势分析工具启动...")
    
    # 导入基础分析模块
    from a_stock_trend_analyzer import (
        load_stock_data, analyze_trend_stocks, 
        calculate_trend_metrics, print_top_performers
    )
    
    # 数据文件路径
    file_path = os.path.join('数据分析测试', '涨停抢筹_所有历史数据.json')
    
    # 加载数据
    data = load_stock_data(file_path)
    if not data:
        return
    
    # 分析趋势股
    stock_performance, target_dates = analyze_trend_stocks(data, days_back=90)
    
    if not stock_performance:
        print("没有找到符合条件的股票数据")
        return
    
    # 计算趋势指标
    trend_analysis = calculate_trend_metrics(stock_performance, min_days=5)
    
    if not trend_analysis:
        print("没有找到符合最小交易天数要求的股票")
        return
    
    # 基础分析
    top_performers = print_top_performers(trend_analysis, top_n=10)
    
    # 增强分析
    generate_comprehensive_report(top_performers, stock_performance)
    
    print(f"\n✅ 增强分析完成！")

if __name__ == "__main__":
    main()
