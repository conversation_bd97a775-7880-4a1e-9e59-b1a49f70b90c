
import json
import os

try:
    # Correct the file path for Windows
    file_path = os.path.join('E:', os.sep, 'mycode', 'wuzhuo', '数据分析测试', '涨停抢筹_所有历史数据.json')
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if 'dates' in data and isinstance(data['dates'], dict):
        date_keys = list(data['dates'].keys())
        if not date_keys:
            print("数据文件中没有找到日期。")
        else:
            date_keys.sort()
            earliest_date = date_keys[0]
            latest_date = date_keys[-1]
            print(f"最早的数据日期是: {earliest_date}")
            print(f"最晚的数据日期是: {latest_date}")
    else:
        print("JSON文件格式不正确，找不到 'dates' 对象。")

except FileNotFoundError:
    print(f"错误: 文件未找到 {file_path}")
except json.JSONDecodeError:
    print("错误: 解析JSON文件失败，请检查文件格式。")
except Exception as e:
    print(f"发生未知错误: {e}")
